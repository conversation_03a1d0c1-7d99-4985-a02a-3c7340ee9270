<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="admin-login">
    <div class="admin-login-container">
        <div class="admin-login-background">
            <div class="admin-overlay"></div>
        </div>
        
        <div class="admin-login-content">
            <div class="admin-login-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>UniConnect Admin</span>
                </div>
                <a href="index.html" class="back-home">
                    <i class="fas fa-arrow-left"></i>
                    Back to Website
                </a>
            </div>

            <div class="admin-login-form-container">
                <div class="admin-welcome">
                    <h2>Admin Portal</h2>
                    <p>Secure access to UniConnect Pakistan administration panel</p>
                </div>

                <form id="adminLoginForm" class="admin-form">
                    <div class="form-group">
                        <label for="adminEmail">Admin Email</label>
                        <div class="input-group">
                            <i class="fas fa-user-shield"></i>
                            <input type="email" id="adminEmail" name="email" placeholder="Enter admin email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="adminPassword">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="adminPassword" name="password" placeholder="Enter admin password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('adminPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="adminRole">Role</label>
                        <div class="input-group">
                            <i class="fas fa-user-tag"></i>
                            <select id="adminRole" name="role" required>
                                <option value="">Select Role</option>
                                <option value="super-admin">Super Admin</option>
                                <option value="admin">Admin</option>
                                <option value="moderator">Moderator</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember">
                            <span class="checkmark"></span>
                            Keep me signed in
                        </label>
                        <a href="#" class="forgot-password">Forgot Password?</a>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        Access Admin Panel
                    </button>

                    <div class="security-notice">
                        <i class="fas fa-shield-alt"></i>
                        <p>This is a secure area. All access attempts are logged and monitored.</p>
                    </div>
                </form>

                <div class="demo-credentials admin-demo">
                    <div class="demo-header">
                        <i class="fas fa-info-circle"></i>
                        <strong>Demo Admin Credentials</strong>
                    </div>
                    <div class="demo-details">
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> admin123</p>
                        <p><strong>Role:</strong> Super Admin</p>
                    </div>
                </div>

                <div class="admin-features">
                    <h4>Admin Panel Features</h4>
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>User Management</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-university"></i>
                            <span>University Management</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-brain"></i>
                            <span>Test Management</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-bar"></i>
                            <span>Analytics & Reports</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-cog"></i>
                            <span>System Settings</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Notification function
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                padding: 16px 20px;
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 12px;
                max-width: 400px;
                border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
            const color = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';

            notification.innerHTML = `
                <i class="fas fa-${icon}" style="color: ${color}; font-size: 18px;"></i>
                <span style="color: #374151; font-weight: 500;">${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px; margin-left: auto;">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => notification.remove(), 5000);
        }

        // Admin login functionality
        document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;
            const role = document.getElementById('adminRole').value;

            // Validate form
            if (!email || !password || !role) {
                showNotification('Please fill in all required fields', 'error');
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
            submitBtn.disabled = true;

            // Simulate admin login process
            setTimeout(() => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // Demo credentials check
                if (email === '<EMAIL>' && password === 'admin123' && role === 'super-admin') {
                    showNotification('Admin login successful! Redirecting to dashboard...', 'success');

                    // Store login state
                    localStorage.setItem('adminLoggedIn', 'true');
                    localStorage.setItem('adminEmail', email);
                    localStorage.setItem('adminRole', role);

                    setTimeout(() => {
                        window.location.href = 'admin-dashboard.html';
                    }, 1500);
                } else {
                    showNotification('Invalid credentials. Please check email, password, and role.', 'error');

                    // Highlight demo credentials
                    const demoSection = document.querySelector('.demo-credentials');
                    if (demoSection) {
                        demoSection.style.border = '2px solid #ef4444';
                        demoSection.style.background = 'rgba(239, 68, 68, 0.1)';

                        setTimeout(() => {
                            demoSection.style.border = '';
                            demoSection.style.background = '';
                        }, 3000);
                    }
                }
            }, 2000);
        });

        // Password toggle functionality
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const toggle = input.parentElement.querySelector('.password-toggle i');

            if (input.type === 'password') {
                input.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }

        // Theme toggle function
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            document.querySelectorAll('.theme-option').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.theme-option.${theme}`).classList.add('active');
        }

        // Demo credentials helper
        document.addEventListener('DOMContentLoaded', function() {
            // Add demo credentials info
            const demoInfo = document.createElement('div');
            demoInfo.className = 'demo-credentials';
            demoInfo.innerHTML = `
                <div class="demo-header">
                    <i class="fas fa-info-circle"></i>
                    <strong>Demo Credentials</strong>
                </div>
                <div class="demo-details">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> admin123</p>
                    <p><strong>Role:</strong> Super Admin</p>
                </div>
            `;
            
            document.querySelector('.admin-login-form-container').appendChild(demoInfo);
        });
    </script>

    <style>
        /* Admin Login Specific Styles */
        .admin-login-container {
            min-height: 100vh;
            display: flex;
            position: relative;
        }

        .admin-login-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3a8a, #1e40af, #3b82f6);
            z-index: -1;
        }

        .admin-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
        }

        .admin-login-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-login-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-top: 20px;
        }

        .admin-login-header .logo {
            color: white;
            font-size: 24px;
            font-weight: 700;
        }

        .admin-login-header .logo i {
            color: #fbbf24;
        }

        .admin-login-form-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            flex: 1;
            max-height: fit-content;
        }

        .admin-welcome {
            text-align: center;
            margin-bottom: 30px;
        }

        .admin-welcome h2 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .admin-welcome p {
            color: var(--text-secondary);
            font-size: 16px;
        }

        .admin-form .input-group select {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
            cursor: pointer;
        }

        .security-notice {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 15px;
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            font-size: 14px;
            color: #92400e;
        }

        .admin-features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid var(--border-color);
        }

        .admin-features h4 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .feature-item i {
            color: var(--primary-color);
            width: 16px;
        }

        .demo-credentials {
            margin-top: 25px;
            padding: 20px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 12px;
        }

        .demo-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            color: #0369a1;
            font-size: 14px;
        }

        .demo-details p {
            margin: 5px 0;
            font-size: 13px;
            color: #0369a1;
        }

        @media (max-width: 768px) {
            .admin-login-form-container {
                padding: 30px 20px;
            }
            
            .features-list {
                grid-template-columns: 1fr;
            }
            
            .admin-login-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
        }
    </style>
</body>
</html>
