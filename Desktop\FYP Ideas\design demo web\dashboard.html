<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="dashboard">
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <button class="theme-option light active" onclick="setTheme('light')" title="Light Mode">
            <i class="fas fa-sun"></i>
        </button>
        <button class="theme-option dark" onclick="setTheme('dark')" title="Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="logo">
                <i class="fas fa-university"></i>
                <span>UniConnect Pakistan</span>
            </div>
            <div class="nav-center">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search universities, programs..." id="searchInput">
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <a href="notifications.html" class="nav-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </a>
                    <a href="chatbot.html" class="nav-icon" data-tooltip="AI Assistant">
                        <i class="fas fa-robot"></i>
                    </a>
                    <div class="profile-dropdown">
                        <button class="profile-btn">
                            <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=U" alt="Profile" class="profile-avatar">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="documents.html"><i class="fas fa-file-alt"></i> Documents</a>
                            <a href="#"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="index.html"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <div class="container">
                <div class="welcome-content">
                    <div class="welcome-text">
                        <h1>Welcome back, <span class="user-name">Ahmad</span>! 👋</h1>
                        <p>Ready to take the next step in your educational journey?</p>
                    </div>
                    <div class="quick-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">5</span>
                                <span class="stat-label">Applications</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">3</span>
                                <span class="stat-label">Deadlines</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">2</span>
                                <span class="stat-label">Mock Tests</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Urgent Deadlines -->
        <section class="urgent-deadlines">
            <div class="container">
                <div class="section-header">
                    <h2><i class="fas fa-exclamation-triangle"></i> Urgent Deadlines</h2>
                    <a href="universities.html" class="view-all">View All</a>
                </div>
                <div class="deadline-cards">
                    <div class="deadline-card urgent">
                        <div class="deadline-header">
                            <div class="uni-info">
                                <img src="https://via.placeholder.com/50x50/2563eb/ffffff?text=PU" alt="PU" class="uni-logo">
                                <div>
                                    <h3>University of Punjab</h3>
                                    <p>Computer Science</p>
                                </div>
                            </div>
                            <div class="deadline-badge urgent">
                                <i class="fas fa-clock"></i>
                                2 days left
                            </div>
                        </div>
                        <div class="deadline-actions">
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                            <button class="btn btn-outline btn-sm">View Details</button>
                        </div>
                    </div>
                    
                    <div class="deadline-card warning">
                        <div class="deadline-header">
                            <div class="uni-info">
                                <img src="https://via.placeholder.com/50x50/10b981/ffffff?text=KU" alt="KU" class="uni-logo">
                                <div>
                                    <h3>Karachi University</h3>
                                    <p>Business Administration</p>
                                </div>
                            </div>
                            <div class="deadline-badge warning">
                                <i class="fas fa-clock"></i>
                                5 days left
                            </div>
                        </div>
                        <div class="deadline-actions">
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                            <button class="btn btn-outline btn-sm">View Details</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <div class="container">
                <h2>Quick Actions</h2>
                <div class="actions-grid">
                    <a href="universities.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>Find Universities</h3>
                        <p>Explore programs and requirements</p>
                    </a>
                    
                    <a href="apply.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3>Apply Now</h3>
                        <p>Start your application process</p>
                    </a>
                    
                    <a href="documents.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <h3>Upload Documents</h3>
                        <p>Manage your certificates</p>
                    </a>
                    
                    <a href="mock-test.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3>Mock Tests</h3>
                        <p>Practice for entry exams</p>
                    </a>
                    
                    <a href="resources.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3>Study Resources</h3>
                        <p>Access preparation materials</p>
                    </a>
                    
                    <a href="chatbot.html" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3>AI Assistant</h3>
                        <p>Get instant help</p>
                    </a>
                </div>
            </div>
        </section>

        <!-- Recommended Universities -->
        <section class="recommended-section">
            <div class="container">
                <div class="section-header">
                    <h2><i class="fas fa-star"></i> Recommended for You</h2>
                    <a href="universities.html" class="view-all">View All</a>
                </div>
                <div class="university-cards">
                    <div class="university-card">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/60x60/2563eb/ffffff?text=LUMS" alt="LUMS" class="uni-logo">
                            <div class="uni-info">
                                <h3>LUMS</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Lahore</p>
                            </div>
                            <button class="bookmark-btn">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="program-tag">MBA Program</div>
                            <p class="uni-description">Leading business school with excellent placement record</p>
                            <div class="card-footer">
                                <span class="deadline">Deadline: Oct 1, 2024</span>
                                <button class="btn btn-primary btn-sm">View Details</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="university-card">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/60x60/10b981/ffffff?text=NUST" alt="NUST" class="uni-logo">
                            <div class="uni-info">
                                <h3>NUST</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Islamabad</p>
                            </div>
                            <button class="bookmark-btn">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="program-tag">Engineering</div>
                            <p class="uni-description">Top engineering university with modern facilities</p>
                            <div class="card-footer">
                                <span class="deadline">Deadline: Sep 20, 2024</span>
                                <button class="btn btn-primary btn-sm">View Details</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="university-card">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/60x60/f59e0b/ffffff?text=IBA" alt="IBA" class="uni-logo">
                            <div class="uni-info">
                                <h3>IBA Karachi</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Karachi</p>
                            </div>
                            <button class="bookmark-btn">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="program-tag">Business</div>
                            <p class="uni-description">Premier business education institution</p>
                            <div class="card-footer">
                                <span class="deadline">Deadline: Aug 25, 2024</span>
                                <button class="btn btn-primary btn-sm">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        // Dashboard specific functionality
        function initializeDashboard() {
            // Profile dropdown toggle
            const profileBtn = document.querySelector('.profile-btn');
            const dropdownMenu = document.querySelector('.dropdown-menu');
            
            profileBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdownMenu.classList.toggle('active');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                dropdownMenu.classList.remove('active');
            });
            
            // Bookmark functionality
            document.querySelectorAll('.bookmark-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('far')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        showNotification('University saved to bookmarks!', 'success');
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        showNotification('University removed from bookmarks!', 'info');
                    }
                });
            });
            
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const query = this.value.trim();
                        if (query) {
                            window.location.href = `universities.html?search=${encodeURIComponent(query)}`;
                        }
                    }
                });
            }
        }
        
        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>
