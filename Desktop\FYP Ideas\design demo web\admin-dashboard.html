<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="admin-dashboard">
    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <div class="admin-logo">
                <i class="fas fa-shield-alt"></i>
                <span>UniConnect Admin</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="admin-dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                        <span class="nav-badge">1,245</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-universities.html" class="nav-link">
                        <i class="fas fa-university"></i>
                        <span>Universities</span>
                        <span class="nav-badge">150</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-applications.html" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        <span>Applications</span>
                        <span class="nav-badge">3,456</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-tests.html" class="nav-link">
                        <i class="fas fa-brain"></i>
                        <span>Mock Tests</span>
                        <span class="nav-badge">89</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-notifications.html" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                        <span class="nav-badge new">12</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="admin-profile">
                <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=A" alt="Admin" class="admin-avatar">
                <div class="admin-info">
                    <span class="admin-name">Admin User</span>
                    <span class="admin-role">Super Admin</span>
                </div>
            </div>
            <button class="logout-btn" onclick="adminLogout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </aside>

    <!-- Main Admin Content -->
    <main class="admin-main">
        <!-- Admin Header -->
        <header class="admin-header">
            <div class="header-left">
                <h1>Dashboard Overview</h1>
                <p>Welcome back, Admin! Here's what's happening today.</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="action-btn" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <div class="notifications-dropdown">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count">5</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Stats -->
        <section class="dashboard-stats">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Total Users</h3>
                        <div class="stat-number">1,245</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12% from last month
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon universities">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Universities</h3>
                        <div class="stat-number">150</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +5 new this month
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon applications">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Applications</h3>
                        <div class="stat-number">3,456</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +18% from last month
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon tests">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Tests Taken</h3>
                        <div class="stat-number">12,890</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +25% from last month
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions-section">
            <h2>Quick Actions</h2>
            <div class="quick-actions-grid">
                <div class="quick-action-card" onclick="window.location.href='admin-users.html'">
                    <div class="action-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3>Add New User</h3>
                    <p>Create a new user account</p>
                </div>

                <div class="quick-action-card" onclick="window.location.href='admin-universities.html'">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h3>Add University</h3>
                    <p>Register a new university</p>
                </div>

                <div class="quick-action-card" onclick="window.location.href='admin-tests.html'">
                    <div class="action-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>Create Mock Test</h3>
                    <p>Design a new practice test</p>
                </div>

                <div class="quick-action-card" onclick="window.location.href='admin-notifications.html'">
                    <div class="action-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3>Send Notification</h3>
                    <p>Broadcast to all users</p>
                </div>
            </div>
        </section>

        <!-- Recent Activity & Analytics -->
        <section class="dashboard-content">
            <div class="content-grid">
                <!-- Recent Activity -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>Recent Activity</h3>
                        <a href="admin-analytics.html" class="view-all">View All</a>
                    </div>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon user">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>Ahmad Khan</strong> registered a new account</p>
                                <span class="activity-time">2 minutes ago</span>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon application">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>Sarah Ahmed</strong> submitted application to LUMS</p>
                                <span class="activity-time">15 minutes ago</span>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon test">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>Ali Hassan</strong> completed MDCAT mock test</p>
                                <span class="activity-time">1 hour ago</span>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon university">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>COMSATS University</strong> updated admission requirements</p>
                                <span class="activity-time">3 hours ago</span>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon user">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>25 new users</strong> registered today</p>
                                <span class="activity-time">6 hours ago</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>System Status</h3>
                        <span class="status-indicator online">Online</span>
                    </div>
                    <div class="system-metrics">
                        <div class="metric">
                            <div class="metric-label">Server Uptime</div>
                            <div class="metric-value">99.9%</div>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: 99.9%"></div>
                            </div>
                        </div>

                        <div class="metric">
                            <div class="metric-label">Database Performance</div>
                            <div class="metric-value">95.2%</div>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: 95.2%"></div>
                            </div>
                        </div>

                        <div class="metric">
                            <div class="metric-label">API Response Time</div>
                            <div class="metric-value">120ms</div>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: 85%"></div>
                            </div>
                        </div>

                        <div class="metric">
                            <div class="metric-label">Storage Usage</div>
                            <div class="metric-value">67%</div>
                            <div class="metric-bar">
                                <div class="metric-fill" style="width: 67%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Universities -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>Top Universities by Applications</h3>
                        <a href="admin-universities.html" class="view-all">View All</a>
                    </div>
                    <div class="top-universities">
                        <div class="university-rank">
                            <div class="rank-number">1</div>
                            <div class="university-info">
                                <h4>LUMS</h4>
                                <p>456 applications</p>
                            </div>
                            <div class="trend up">
                                <i class="fas fa-arrow-up"></i>
                                +15%
                            </div>
                        </div>

                        <div class="university-rank">
                            <div class="rank-number">2</div>
                            <div class="university-info">
                                <h4>University of Punjab</h4>
                                <p>389 applications</p>
                            </div>
                            <div class="trend up">
                                <i class="fas fa-arrow-up"></i>
                                +8%
                            </div>
                        </div>

                        <div class="university-rank">
                            <div class="rank-number">3</div>
                            <div class="university-info">
                                <h4>NUST</h4>
                                <p>342 applications</p>
                            </div>
                            <div class="trend up">
                                <i class="fas fa-arrow-up"></i>
                                +12%
                            </div>
                        </div>

                        <div class="university-rank">
                            <div class="rank-number">4</div>
                            <div class="university-info">
                                <h4>Karachi University</h4>
                                <p>298 applications</p>
                            </div>
                            <div class="trend down">
                                <i class="fas fa-arrow-down"></i>
                                -3%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        function toggleSidebar() {
            document.querySelector('.admin-sidebar').classList.toggle('collapsed');
        }

        function adminLogout() {
            if (confirm('Are you sure you want to logout?')) {
                showNotification('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'admin-login.html';
                }, 1500);
            }
        }

        function refreshData() {
            showNotification('Refreshing dashboard data...', 'info');
            // Simulate data refresh
            setTimeout(() => {
                showNotification('Dashboard data refreshed successfully!', 'success');
            }, 2000);
        }

        function exportData() {
            showNotification('Preparing data export...', 'info');
            setTimeout(() => {
                showNotification('Data exported successfully!', 'success');
            }, 2000);
        }

        // Initialize admin dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate real-time updates
            setInterval(() => {
                // Update notification count randomly
                const notificationCount = document.querySelector('.notification-count');
                if (notificationCount) {
                    const currentCount = parseInt(notificationCount.textContent);
                    if (Math.random() > 0.8) { // 20% chance to update
                        notificationCount.textContent = currentCount + 1;
                    }
                }
            }, 30000); // Every 30 seconds
        });
    </script>
</body>
</html>
