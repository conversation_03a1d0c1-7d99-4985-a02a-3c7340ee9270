<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Dashboard - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="university-dashboard">
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <button class="theme-option light active" onclick="setTheme('light')" title="Light Mode">
            <i class="fas fa-sun"></i>
        </button>
        <button class="theme-option dark" onclick="setTheme('dark')" title="Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <div class="university-layout">
        <!-- University Sidebar -->
        <aside class="university-sidebar">
            <div class="sidebar-header">
                <div class="university-logo">
                    <i class="fas fa-university"></i>
                    <span>University of Punjab</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

        <nav class="sidebar-nav">
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="university-dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-applications.html" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        <span>Applications</span>
                        <span class="nav-badge">156</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-programs.html" class="nav-link">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Programs</span>
                        <span class="nav-badge">15</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-challans.html" class="nav-link">
                        <i class="fas fa-money-check-alt"></i>
                        <span>Challans</span>
                        <span class="nav-badge">89</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-students.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Students</span>
                        <span class="nav-badge">2,456</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-cms.html" class="nav-link">
                        <i class="fas fa-edit"></i>
                        <span>Content Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-notifications.html" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                        <span class="nav-badge new">8</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="university-settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="university-profile">
                <img src="https://via.placeholder.com/40x40/059669/ffffff?text=PU" alt="University" class="university-avatar">
                <div class="university-info">
                    <span class="university-name">University of Punjab</span>
                    <span class="university-role">Admin Portal</span>
                </div>
            </div>
            <button class="logout-btn" onclick="universityLogout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
        </aside>

    <!-- Main University Content -->
    <main class="university-main">
        <!-- University Header -->
        <header class="university-header">
            <div class="header-left">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="header-text">
                    <h1>University Dashboard</h1>
                    <p>Welcome back! Here's what's happening at your university today.</p>
                </div>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <button class="action-btn" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <div class="notifications-dropdown">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count">8</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Stats -->
        <section class="dashboard-stats">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon applications">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3>New Applications</h3>
                        <div class="stat-number">156</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +23% from last week
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Pending Reviews</h3>
                        <div class="stat-number">89</div>
                        <div class="stat-change warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Requires attention
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon challans">
                        <i class="fas fa-money-check-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Challans Generated</h3>
                        <div class="stat-number">234</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15% from last week
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon enrolled">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Enrolled Students</h3>
                        <div class="stat-number">2,456</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8% from last year
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions-section">
            <h2>Quick Actions</h2>
            <div class="quick-actions-grid">
                <div class="quick-action-card" onclick="window.location.href='university-applications.html'">
                    <div class="action-icon">
                        <i class="fas fa-file-plus"></i>
                    </div>
                    <h3>Review Applications</h3>
                    <p>Review and process new student applications</p>
                </div>

                <div class="quick-action-card" onclick="window.location.href='university-programs.html'">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h3>Add Program</h3>
                    <p>Create new academic programs</p>
                </div>

                <div class="quick-action-card" onclick="window.location.href='university-challans.html'">
                    <div class="action-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <h3>Generate Challan</h3>
                    <p>Create payment challans for students</p>
                </div>

                <div class="quick-action-card" onclick="window.location.href='university-notifications.html'">
                    <div class="action-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3>Send Notification</h3>
                    <p>Broadcast messages to students</p>
                </div>
            </div>
        </section>

        <!-- Recent Activity & Analytics -->
        <section class="dashboard-content">
            <div class="content-grid">
                <!-- Recent Applications -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>Recent Applications</h3>
                        <a href="university-applications.html" class="view-all">View All</a>
                    </div>
                    <div class="applications-list">
                        <div class="application-item">
                            <div class="applicant-info">
                                <img src="https://via.placeholder.com/40x40/1e40af/ffffff?text=AK" alt="Student" class="applicant-avatar">
                                <div class="applicant-details">
                                    <h4>Ahmad Khan</h4>
                                    <p>Computer Science - BS</p>
                                </div>
                            </div>
                            <div class="application-status">
                                <span class="status-badge pending">Pending Review</span>
                                <span class="application-time">2 hours ago</span>
                            </div>
                        </div>

                        <div class="application-item">
                            <div class="applicant-info">
                                <img src="https://via.placeholder.com/40x40/059669/ffffff?text=SA" alt="Student" class="applicant-avatar">
                                <div class="applicant-details">
                                    <h4>Sarah Ahmed</h4>
                                    <p>Business Administration - MBA</p>
                                </div>
                            </div>
                            <div class="application-status">
                                <span class="status-badge approved">Approved</span>
                                <span class="application-time">4 hours ago</span>
                            </div>
                        </div>

                        <div class="application-item">
                            <div class="applicant-info">
                                <img src="https://via.placeholder.com/40x40/dc2626/ffffff?text=MH" alt="Student" class="applicant-avatar">
                                <div class="applicant-details">
                                    <h4>Muhammad Hassan</h4>
                                    <p>Engineering - BE</p>
                                </div>
                            </div>
                            <div class="application-status">
                                <span class="status-badge under-review">Under Review</span>
                                <span class="application-time">6 hours ago</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Program Statistics -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>Program Statistics</h3>
                        <a href="university-programs.html" class="view-all">View All</a>
                    </div>
                    <div class="program-stats">
                        <div class="program-stat">
                            <div class="program-info">
                                <h4>Computer Science</h4>
                                <p>BS Program</p>
                            </div>
                            <div class="program-numbers">
                                <span class="applications-count">89 applications</span>
                                <span class="seats-available">45 seats available</span>
                            </div>
                        </div>

                        <div class="program-stat">
                            <div class="program-info">
                                <h4>Business Administration</h4>
                                <p>MBA Program</p>
                            </div>
                            <div class="program-numbers">
                                <span class="applications-count">67 applications</span>
                                <span class="seats-available">30 seats available</span>
                            </div>
                        </div>

                        <div class="program-stat">
                            <div class="program-info">
                                <h4>Engineering</h4>
                                <p>BE Program</p>
                            </div>
                            <div class="program-numbers">
                                <span class="applications-count">123 applications</span>
                                <span class="seats-available">60 seats available</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        </main>
    </div>

    <script>
        // Notification function
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                padding: 16px 20px;
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 12px;
                max-width: 400px;
                border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
            const color = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';

            notification.innerHTML = `
                <i class="fas fa-${icon}" style="color: ${color}; font-size: 18px;"></i>
                <span style="color: #374151; font-weight: 500;">${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px; margin-left: auto;">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => notification.remove(), 5000);
        }
    </script>
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.university-sidebar');
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('mobile-open');
            } else {
                sidebar.classList.toggle('collapsed');
            }
        }

        function universityLogout() {
            if (confirm('Are you sure you want to logout?')) {
                showNotification('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'university-login.html';
                }, 1500);
            }
        }

        function refreshData() {
            showNotification('Refreshing dashboard data...', 'info');
            setTimeout(() => {
                showNotification('Dashboard data refreshed successfully!', 'success');
            }, 2000);
        }

        function exportData() {
            showNotification('Preparing data export...', 'info');
            setTimeout(() => {
                showNotification('Data exported successfully!', 'success');
            }, 2000);
        }

        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.university-sidebar');
            const toggle = document.querySelector('.sidebar-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !toggle.contains(e.target) &&
                sidebar.classList.contains('mobile-open')) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.university-sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // Theme toggle function
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            // Update theme toggle buttons
            document.querySelectorAll('.theme-option').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.theme-option.${theme}`).classList.add('active');
        }

        // Initialize theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            setTheme(savedTheme);
        });
    </script>
</body>
</html>
