<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Login - UniConnect Pakistan</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e40af, #059669);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo i {
            font-size: 48px;
            color: #059669;
            margin-bottom: 10px;
        }

        .logo h1 {
            color: #1e40af;
            font-size: 24px;
            margin-bottom: 5px;
        }

        .logo p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #1e40af;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #1e40af, #059669);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .demo-box {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .demo-box h3 {
            color: #1e40af;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .demo-box p {
            margin-bottom: 8px;
            font-size: 14px;
            color: #555;
        }

        .auto-fill-btn {
            background: #059669;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
        }

        .auto-fill-btn:hover {
            background: #047857;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #1e40af;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-university"></i>
            <h1>University Portal</h1>
            <p>UniConnect Pakistan</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="universityCode">University Code</label>
                <input type="text" id="universityCode" placeholder="Enter university code" required>
            </div>

            <div class="form-group">
                <label for="adminEmail">Admin Email</label>
                <input type="email" id="adminEmail" placeholder="Enter admin email" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" placeholder="Enter password" required>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <i class="fas fa-sign-in-alt"></i>
                Login to University Portal
            </button>
        </form>

        <div class="demo-box">
            <h3><i class="fas fa-info-circle"></i> Demo Credentials</h3>
            <p><strong>University Code:</strong> PU2024</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> university123</p>
            <button class="auto-fill-btn" onclick="fillDemo()">
                <i class="fas fa-magic"></i> Auto Fill
            </button>
        </div>

        <div class="back-link">
            <a href="index.html">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
    </div>

    <script>
        // Auto fill demo credentials
        function fillDemo() {
            document.getElementById('universityCode').value = 'PU2024';
            document.getElementById('adminEmail').value = '<EMAIL>';
            document.getElementById('password').value = 'university123';
            alert('Demo credentials filled! Now click Login button.');
        }

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const code = document.getElementById('universityCode').value;
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('password').value;
            
            if (!code || !email || !password) {
                alert('Please fill all fields!');
                return;
            }
            
            const btn = document.getElementById('loginBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
            btn.disabled = true;
            
            setTimeout(() => {
                if (code === 'PU2024' && email === '<EMAIL>' && password === 'university123') {
                    alert('Login Successful! Redirecting to dashboard...');
                    window.location.href = 'university-dashboard.html';
                } else {
                    alert('Invalid credentials! Please use demo credentials.');
                    btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login to University Portal';
                    btn.disabled = false;
                }
            }, 1500);
        });

        // Test button click
        console.log('University login page loaded successfully!');
    </script>
</body>
</html>
