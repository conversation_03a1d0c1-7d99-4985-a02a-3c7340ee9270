<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Tests - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="mock-test">
    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="logo">
                <a href="dashboard.html">
                    <i class="fas fa-university"></i>
                    <span>UniConnect Pakistan</span>
                </a>
            </div>
            <div class="nav-center">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search mock tests..." id="searchInput">
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <a href="notifications.html" class="nav-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </a>
                    <a href="chatbot.html" class="nav-icon" data-tooltip="AI Assistant">
                        <i class="fas fa-robot"></i>
                    </a>
                    <div class="profile-dropdown">
                        <button class="profile-btn">
                            <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=U" alt="Profile" class="profile-avatar">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="documents.html"><i class="fas fa-file-alt"></i> Documents</a>
                            <a href="#"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="index.html"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="mock-test-main">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="header-content">
                    <div class="breadcrumb">
                        <a href="dashboard.html">Dashboard</a>
                        <i class="fas fa-chevron-right"></i>
                        <span>Mock Tests</span>
                    </div>
                    <h1>Mock Tests & Practice</h1>
                    <p>Prepare for your entry tests with our comprehensive mock test platform</p>
                </div>
                <div class="header-stats">
                    <div class="stat">
                        <span class="stat-number">15</span>
                        <span class="stat-label">Tests Taken</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">78%</span>
                        <span class="stat-label">Avg Score</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">5</span>
                        <span class="stat-label">Subjects</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upcoming Tests -->
        <section class="upcoming-tests">
            <div class="container">
                <h2><i class="fas fa-calendar-alt"></i> Upcoming Live Tests</h2>
                <div class="test-cards">
                    <div class="test-card live">
                        <div class="test-header">
                            <div class="test-info">
                                <h3>MDCAT Mock Test #5</h3>
                                <p>Medical College Admission Test</p>
                            </div>
                            <div class="test-status live">
                                <i class="fas fa-circle"></i>
                                Live Now
                            </div>
                        </div>
                        <div class="test-details">
                            <div class="detail">
                                <i class="fas fa-clock"></i>
                                <span>Duration: 3 hours</span>
                            </div>
                            <div class="detail">
                                <i class="fas fa-question-circle"></i>
                                <span>200 Questions</span>
                            </div>
                            <div class="detail">
                                <i class="fas fa-users"></i>
                                <span>1,245 participants</span>
                            </div>
                        </div>
                        <div class="test-actions">
                            <button class="btn btn-primary btn-full">
                                <i class="fas fa-play"></i>
                                Start Test Now
                            </button>
                        </div>
                    </div>

                    <div class="test-card upcoming">
                        <div class="test-header">
                            <div class="test-info">
                                <h3>ECAT Practice Test</h3>
                                <p>Engineering College Admission Test</p>
                            </div>
                            <div class="test-status upcoming">
                                <i class="fas fa-clock"></i>
                                Starts in 2 hours
                            </div>
                        </div>
                        <div class="test-details">
                            <div class="detail">
                                <i class="fas fa-clock"></i>
                                <span>Duration: 2.5 hours</span>
                            </div>
                            <div class="detail">
                                <i class="fas fa-question-circle"></i>
                                <span>150 Questions</span>
                            </div>
                            <div class="detail">
                                <i class="fas fa-calendar"></i>
                                <span>Aug 10, 2024 - 2:00 PM</span>
                            </div>
                        </div>
                        <div class="test-actions">
                            <button class="btn btn-outline btn-full">
                                <i class="fas fa-bell"></i>
                                Set Reminder
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Categories -->
        <section class="test-categories">
            <div class="container">
                <h2>Practice by Category</h2>
                <div class="categories-grid">
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <h3>MDCAT</h3>
                        <p>Medical College Admission Test</p>
                        <div class="category-stats">
                            <span>12 Tests Available</span>
                            <span>•</span>
                            <span>3 Completed</span>
                        </div>
                        <button class="btn btn-primary">Practice Now</button>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3>ECAT</h3>
                        <p>Engineering College Admission Test</p>
                        <div class="category-stats">
                            <span>10 Tests Available</span>
                            <span>•</span>
                            <span>5 Completed</span>
                        </div>
                        <button class="btn btn-primary">Practice Now</button>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h3>Business Tests</h3>
                        <p>MBA & BBA Admission Tests</p>
                        <div class="category-stats">
                            <span>8 Tests Available</span>
                            <span>•</span>
                            <span>2 Completed</span>
                        </div>
                        <button class="btn btn-primary">Practice Now</button>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-laptop-code"></i>
                        </div>
                        <h3>Computer Science</h3>
                        <p>CS & IT Admission Tests</p>
                        <div class="category-stats">
                            <span>15 Tests Available</span>
                            <span>•</span>
                            <span>7 Completed</span>
                        </div>
                        <button class="btn btn-primary">Practice Now</button>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3>General Knowledge</h3>
                        <p>Current Affairs & GK Tests</p>
                        <div class="category-stats">
                            <span>20 Tests Available</span>
                            <span>•</span>
                            <span>8 Completed</span>
                        </div>
                        <button class="btn btn-primary">Practice Now</button>
                    </div>

                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <h3>English</h3>
                        <p>English Language Tests</p>
                        <div class="category-stats">
                            <span>18 Tests Available</span>
                            <span>•</span>
                            <span>6 Completed</span>
                        </div>
                        <button class="btn btn-primary">Practice Now</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recent Attempts -->
        <section class="recent-attempts">
            <div class="container">
                <div class="section-header">
                    <h2><i class="fas fa-history"></i> Recent Attempts</h2>
                    <a href="#" class="view-all">View All Results</a>
                </div>
                <div class="attempts-list">
                    <div class="attempt-card">
                        <div class="attempt-info">
                            <div class="test-details">
                                <h4>MDCAT Mock Test #4</h4>
                                <p>Completed on Aug 8, 2024</p>
                            </div>
                            <div class="score-badge excellent">
                                <span class="score">85%</span>
                                <span class="label">Excellent</span>
                            </div>
                        </div>
                        <div class="attempt-stats">
                            <div class="stat">
                                <span class="value">170/200</span>
                                <span class="label">Correct</span>
                            </div>
                            <div class="stat">
                                <span class="value">2h 45m</span>
                                <span class="label">Time Taken</span>
                            </div>
                            <div class="stat">
                                <span class="value">Top 15%</span>
                                <span class="label">Rank</span>
                            </div>
                        </div>
                        <div class="attempt-actions">
                            <button class="btn btn-outline btn-sm">View Report</button>
                            <button class="btn btn-primary btn-sm">Retake Test</button>
                        </div>
                    </div>

                    <div class="attempt-card">
                        <div class="attempt-info">
                            <div class="test-details">
                                <h4>ECAT Practice Test #3</h4>
                                <p>Completed on Aug 6, 2024</p>
                            </div>
                            <div class="score-badge good">
                                <span class="score">72%</span>
                                <span class="label">Good</span>
                            </div>
                        </div>
                        <div class="attempt-stats">
                            <div class="stat">
                                <span class="value">108/150</span>
                                <span class="label">Correct</span>
                            </div>
                            <div class="stat">
                                <span class="value">2h 20m</span>
                                <span class="label">Time Taken</span>
                            </div>
                            <div class="stat">
                                <span class="value">Top 35%</span>
                                <span class="label">Rank</span>
                            </div>
                        </div>
                        <div class="attempt-actions">
                            <button class="btn btn-outline btn-sm">View Report</button>
                            <button class="btn btn-primary btn-sm">Retake Test</button>
                        </div>
                    </div>

                    <div class="attempt-card">
                        <div class="attempt-info">
                            <div class="test-details">
                                <h4>Business Aptitude Test</h4>
                                <p>Completed on Aug 4, 2024</p>
                            </div>
                            <div class="score-badge average">
                                <span class="score">68%</span>
                                <span class="label">Average</span>
                            </div>
                        </div>
                        <div class="attempt-stats">
                            <div class="stat">
                                <span class="value">68/100</span>
                                <span class="label">Correct</span>
                            </div>
                            <div class="stat">
                                <span class="value">1h 45m</span>
                                <span class="label">Time Taken</span>
                            </div>
                            <div class="stat">
                                <span class="value">Top 50%</span>
                                <span class="label">Rank</span>
                            </div>
                        </div>
                        <div class="attempt-actions">
                            <button class="btn btn-outline btn-sm">View Report</button>
                            <button class="btn btn-primary btn-sm">Retake Test</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Analytics -->
        <section class="performance-section">
            <div class="container">
                <h2><i class="fas fa-chart-line"></i> Performance Analytics</h2>
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Subject-wise Performance</h3>
                        <div class="subject-scores">
                            <div class="subject-score">
                                <span class="subject">Physics</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: 85%"></div>
                                </div>
                                <span class="percentage">85%</span>
                            </div>
                            <div class="subject-score">
                                <span class="subject">Chemistry</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: 78%"></div>
                                </div>
                                <span class="percentage">78%</span>
                            </div>
                            <div class="subject-score">
                                <span class="subject">Biology</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: 92%"></div>
                                </div>
                                <span class="percentage">92%</span>
                            </div>
                            <div class="subject-score">
                                <span class="subject">English</span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: 70%"></div>
                                </div>
                                <span class="percentage">70%</span>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <h3>Progress Trend</h3>
                        <div class="trend-chart">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line"></i>
                                <p>Your scores are improving!</p>
                                <span>+12% from last month</span>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-card">
                        <h3>Strengths & Weaknesses</h3>
                        <div class="strengths-weaknesses">
                            <div class="strength-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Strong in Biology concepts</span>
                            </div>
                            <div class="strength-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Good time management</span>
                            </div>
                            <div class="weakness-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Need improvement in English</span>
                            </div>
                            <div class="weakness-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Physics numerical problems</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        function initializeMockTest() {
            // Start test functionality
            document.querySelectorAll('.btn:contains("Start Test Now")').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (confirm('Are you ready to start the test? Once started, the timer will begin.')) {
                        window.location.href = 'test-attempt.html';
                    }
                });
            });

            // Set reminder functionality
            document.querySelectorAll('.btn:contains("Set Reminder")').forEach(btn => {
                btn.addEventListener('click', function() {
                    showNotification('Reminder set! You will be notified 15 minutes before the test.', 'success');
                });
            });

            // Practice now buttons
            document.querySelectorAll('.category-card .btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.closest('.category-card').querySelector('h3').textContent;
                    showNotification(`Loading ${category} practice tests...`, 'info');
                    setTimeout(() => {
                        window.location.href = 'test-attempt.html';
                    }, 1500);
                });
            });

            // View report buttons
            document.querySelectorAll('.btn:contains("View Report")').forEach(btn => {
                btn.addEventListener('click', function() {
                    showNotification('Loading detailed test report...', 'info');
                });
            });

            // Retake test buttons
            document.querySelectorAll('.btn:contains("Retake Test")').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to retake this test?')) {
                        window.location.href = 'test-attempt.html';
                    }
                });
            });
        }

        document.addEventListener('DOMContentLoaded', initializeMockTest);
    </script>
</body>
</html>
