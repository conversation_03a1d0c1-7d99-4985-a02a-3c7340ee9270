<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universities - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="universities">
    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="logo">
                <a href="dashboard.html">
                    <i class="fas fa-university"></i>
                    <span>UniConnect Pakistan</span>
                </a>
            </div>
            <div class="nav-center">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search universities, programs..." id="searchInput">
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <a href="notifications.html" class="nav-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </a>
                    <a href="chatbot.html" class="nav-icon" data-tooltip="AI Assistant">
                        <i class="fas fa-robot"></i>
                    </a>
                    <div class="profile-dropdown">
                        <button class="profile-btn">
                            <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=U" alt="Profile" class="profile-avatar">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="documents.html"><i class="fas fa-file-alt"></i> Documents</a>
                            <a href="#"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="index.html"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="universities-main">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="header-content">
                    <div class="breadcrumb">
                        <a href="dashboard.html">Dashboard</a>
                        <i class="fas fa-chevron-right"></i>
                        <span>Universities</span>
                    </div>
                    <h1>Explore Universities</h1>
                    <p>Discover the perfect university and program for your future</p>
                </div>
                <div class="header-stats">
                    <div class="stat">
                        <span class="stat-number">150+</span>
                        <span class="stat-label">Universities</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Programs</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">25</span>
                        <span class="stat-label">Cities</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Filters Section -->
        <section class="filters-section">
            <div class="container">
                <div class="filters-header">
                    <h3>Filter Universities</h3>
                    <button class="clear-filters">Clear All</button>
                </div>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label>City</label>
                        <select id="cityFilter">
                            <option value="">All Cities</option>
                            <option value="lahore">Lahore</option>
                            <option value="karachi">Karachi</option>
                            <option value="islamabad">Islamabad</option>
                            <option value="peshawar">Peshawar</option>
                            <option value="quetta">Quetta</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Program Type</label>
                        <select id="programFilter">
                            <option value="">All Programs</option>
                            <option value="engineering">Engineering</option>
                            <option value="business">Business</option>
                            <option value="medicine">Medicine</option>
                            <option value="computer-science">Computer Science</option>
                            <option value="arts">Arts & Humanities</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Deadline</label>
                        <select id="deadlineFilter">
                            <option value="">All Deadlines</option>
                            <option value="urgent">Urgent (< 7 days)</option>
                            <option value="soon">Soon (< 30 days)</option>
                            <option value="open">Open Applications</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Entry Test</label>
                        <select id="testFilter">
                            <option value="">All</option>
                            <option value="required">Required</option>
                            <option value="not-required">Not Required</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- Universities List -->
        <section class="universities-list">
            <div class="container">
                <div class="list-header">
                    <div class="results-info">
                        <span id="resultsCount">Showing 12 of 150+ universities</span>
                    </div>
                    <div class="sort-options">
                        <label>Sort by:</label>
                        <select id="sortBy">
                            <option value="relevance">Relevance</option>
                            <option value="deadline">Deadline</option>
                            <option value="name">Name</option>
                            <option value="city">City</option>
                        </select>
                    </div>
                </div>

                <div class="universities-grid" id="universitiesGrid">
                    <!-- University Card 1 -->
                    <div class="university-card" data-city="lahore" data-program="computer-science" data-deadline="urgent">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/2563eb/ffffff?text=PU" alt="PU" class="uni-logo">
                            <div class="uni-info">
                                <h3>University of Punjab</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Lahore, Punjab</p>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <span>4.2</span>
                                </div>
                            </div>
                            <button class="bookmark-btn">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag">Computer Science</span>
                                <span class="program-tag">Engineering</span>
                                <span class="program-tag">Business</span>
                            </div>
                            <p class="uni-description">One of Pakistan's oldest and most prestigious universities offering diverse academic programs.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text urgent">Deadline: Aug 15, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: PKR 50,000/year</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm">View Details</button>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 2 -->
                    <div class="university-card" data-city="karachi" data-program="business" data-deadline="soon">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/10b981/ffffff?text=KU" alt="KU" class="uni-logo">
                            <div class="uni-info">
                                <h3>University of Karachi</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Karachi, Sindh</p>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.5</span>
                                </div>
                            </div>
                            <button class="bookmark-btn">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag">Business Administration</span>
                                <span class="program-tag">Medicine</span>
                                <span class="program-tag">Arts</span>
                            </div>
                            <p class="uni-description">Leading public university in Karachi with excellent research facilities and diverse programs.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text warning">Deadline: Aug 30, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: PKR 45,000/year</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm">View Details</button>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 3 -->
                    <div class="university-card" data-city="lahore" data-program="business" data-deadline="open">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/f59e0b/ffffff?text=LUMS" alt="LUMS" class="uni-logo">
                            <div class="uni-info">
                                <h3>LUMS</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Lahore, Punjab</p>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                </div>
                            </div>
                            <button class="bookmark-btn">
                                <i class="fas fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag">MBA</span>
                                <span class="program-tag">Computer Science</span>
                                <span class="program-tag">Economics</span>
                            </div>
                            <p class="uni-description">Premier private university known for business education and research excellence.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text normal">Deadline: Oct 1, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: PKR 200,000/year</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm">View Details</button>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 4 -->
                    <div class="university-card" data-city="islamabad" data-program="engineering" data-deadline="open">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/ef4444/ffffff?text=NUST" alt="NUST" class="uni-logo">
                            <div class="uni-info">
                                <h3>NUST</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Islamabad</p>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.7</span>
                                </div>
                            </div>
                            <button class="bookmark-btn">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag">Engineering</span>
                                <span class="program-tag">Computer Science</span>
                                <span class="program-tag">Management</span>
                            </div>
                            <p class="uni-description">Top-ranked engineering university with state-of-the-art facilities and research programs.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text normal">Deadline: Sep 20, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: PKR 150,000/year</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm">View Details</button>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Load More Button -->
                <div class="load-more-section">
                    <button class="btn btn-outline btn-large" id="loadMoreBtn">
                        <i class="fas fa-plus"></i>
                        Load More Universities
                    </button>
                </div>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        // Universities page functionality
        function initializeUniversitiesList() {
            // Filter functionality
            const filters = {
                city: document.getElementById('cityFilter'),
                program: document.getElementById('programFilter'),
                deadline: document.getElementById('deadlineFilter'),
                test: document.getElementById('testFilter')
            };

            Object.values(filters).forEach(filter => {
                filter.addEventListener('change', applyFilters);
            });

            // Clear filters
            document.querySelector('.clear-filters').addEventListener('click', clearFilters);

            // Sort functionality
            document.getElementById('sortBy').addEventListener('change', sortUniversities);

            // Bookmark functionality
            document.querySelectorAll('.bookmark-btn').forEach(btn => {
                btn.addEventListener('click', toggleBookmark);
            });

            // Load more functionality
            document.getElementById('loadMoreBtn').addEventListener('click', loadMoreUniversities);
        }

        function applyFilters() {
            const cards = document.querySelectorAll('.university-card');
            const cityFilter = document.getElementById('cityFilter').value;
            const programFilter = document.getElementById('programFilter').value;
            const deadlineFilter = document.getElementById('deadlineFilter').value;
            
            let visibleCount = 0;

            cards.forEach(card => {
                let show = true;

                if (cityFilter && !card.dataset.city.includes(cityFilter)) {
                    show = false;
                }

                if (programFilter && !card.dataset.program.includes(programFilter)) {
                    show = false;
                }

                if (deadlineFilter && card.dataset.deadline !== deadlineFilter) {
                    show = false;
                }

                if (show) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            document.getElementById('resultsCount').textContent = `Showing ${visibleCount} universities`;
        }

        function clearFilters() {
            document.getElementById('cityFilter').value = '';
            document.getElementById('programFilter').value = '';
            document.getElementById('deadlineFilter').value = '';
            document.getElementById('testFilter').value = '';
            
            document.querySelectorAll('.university-card').forEach(card => {
                card.style.display = 'block';
            });
            
            document.getElementById('resultsCount').textContent = 'Showing 4 of 150+ universities';
        }

        function sortUniversities() {
            const sortBy = document.getElementById('sortBy').value;
            const grid = document.getElementById('universitiesGrid');
            const cards = Array.from(grid.children);

            cards.sort((a, b) => {
                switch (sortBy) {
                    case 'name':
                        return a.querySelector('h3').textContent.localeCompare(b.querySelector('h3').textContent);
                    case 'city':
                        return a.dataset.city.localeCompare(b.dataset.city);
                    case 'deadline':
                        const deadlineOrder = { urgent: 1, soon: 2, open: 3 };
                        return deadlineOrder[a.dataset.deadline] - deadlineOrder[b.dataset.deadline];
                    default:
                        return 0;
                }
            });

            cards.forEach(card => grid.appendChild(card));
        }

        function toggleBookmark(event) {
            event.preventDefault();
            const icon = this.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showNotification('University saved to bookmarks!', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showNotification('University removed from bookmarks!', 'info');
            }
        }

        function loadMoreUniversities() {
            showNotification('Loading more universities...', 'info');
            // Simulate loading delay
            setTimeout(() => {
                showNotification('More universities loaded!', 'success');
            }, 1500);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeUniversitiesList);
    </script>
</body>
</html>
