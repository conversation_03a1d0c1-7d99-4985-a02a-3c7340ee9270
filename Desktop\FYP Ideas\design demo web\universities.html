<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universities - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="universities">
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <button class="theme-option light active" onclick="setTheme('light')" title="Light Mode">
            <i class="fas fa-sun"></i>
        </button>
        <button class="theme-option dark" onclick="setTheme('dark')" title="Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="logo">
                <a href="dashboard.html">
                    <i class="fas fa-university"></i>
                    <span>UniConnect Pakistan</span>
                </a>
            </div>
            <div class="nav-center">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search universities, programs..." id="searchInput">
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <a href="notifications.html" class="nav-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </a>
                    <a href="chatbot.html" class="nav-icon" data-tooltip="AI Assistant">
                        <i class="fas fa-robot"></i>
                    </a>
                    <div class="profile-dropdown">
                        <button class="profile-btn">
                            <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=U" alt="Profile" class="profile-avatar">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="documents.html"><i class="fas fa-file-alt"></i> Documents</a>
                            <a href="#"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="index.html"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="universities-main">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="header-content">
                    <div class="breadcrumb">
                        <a href="dashboard.html">Dashboard</a>
                        <i class="fas fa-chevron-right"></i>
                        <span>Universities</span>
                    </div>
                    <h1>Universities Catalog</h1>
                    <p>Find your perfect university match from Pakistan's top institutions</p>
                </div>
                <div class="header-stats">
                    <div class="stat">
                        <span class="stat-number">150+</span>
                        <span class="stat-label">Universities</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Programs</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">25</span>
                        <span class="stat-label">Cities</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">₨50K-2M</span>
                        <span class="stat-label">Fee Range</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Filters Section -->
        <section class="filters-section">
            <div class="container">
                <div class="filters-header">
                    <h3>Filter Universities</h3>
                    <div class="filter-actions">
                        <button class="btn btn-outline btn-sm" onclick="saveFilters()">
                            <i class="fas fa-bookmark"></i>
                            Save Filters
                        </button>
                        <button class="clear-filters">Clear All</button>
                    </div>
                </div>
                <div class="filters-grid">
                    <div class="filter-group">
                        <label>City</label>
                        <select id="cityFilter">
                            <option value="">All Cities</option>
                            <option value="lahore">Lahore</option>
                            <option value="karachi">Karachi</option>
                            <option value="islamabad">Islamabad</option>
                            <option value="rawalpindi">Rawalpindi</option>
                            <option value="faisalabad">Faisalabad</option>
                            <option value="multan">Multan</option>
                            <option value="peshawar">Peshawar</option>
                            <option value="quetta">Quetta</option>
                            <option value="hyderabad">Hyderabad</option>
                            <option value="gujranwala">Gujranwala</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Program Type</label>
                        <select id="programFilter">
                            <option value="">All Programs</option>
                            <option value="engineering">Engineering</option>
                            <option value="business">Business Administration</option>
                            <option value="medicine">Medicine & Health Sciences</option>
                            <option value="computer-science">Computer Science & IT</option>
                            <option value="arts">Arts & Humanities</option>
                            <option value="law">Law</option>
                            <option value="agriculture">Agriculture</option>
                            <option value="pharmacy">Pharmacy</option>
                            <option value="education">Education</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Fee Range</label>
                        <select id="feeFilter">
                            <option value="">All Fee Ranges</option>
                            <option value="0-100000">₨0 - ₨100,000</option>
                            <option value="100000-300000">₨100,000 - ₨300,000</option>
                            <option value="300000-500000">₨300,000 - ₨500,000</option>
                            <option value="500000-1000000">₨500,000 - ₨1,000,000</option>
                            <option value="1000000+">₨1,000,000+</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Deadline</label>
                        <select id="deadlineFilter">
                            <option value="">All Deadlines</option>
                            <option value="urgent">Urgent (< 7 days)</option>
                            <option value="soon">Soon (< 30 days)</option>
                            <option value="open">Open Applications</option>
                            <option value="closed">Closed</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Entry Test</label>
                        <select id="testFilter">
                            <option value="">All</option>
                            <option value="required">Required</option>
                            <option value="not-required">Not Required</option>
                            <option value="optional">Optional</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>University Type</label>
                        <select id="typeFilter">
                            <option value="">All Types</option>
                            <option value="public">Public</option>
                            <option value="private">Private</option>
                            <option value="semi-government">Semi-Government</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- Universities List -->
        <section class="universities-list">
            <div class="container">
                <div class="list-header">
                    <div class="results-info">
                        <span id="resultsCount">Showing 12 of 150+ universities</span>
                    </div>
                    <div class="sort-options">
                        <label>Sort by:</label>
                        <select id="sortBy">
                            <option value="relevance">Relevance</option>
                            <option value="deadline">Deadline</option>
                            <option value="name">Name</option>
                            <option value="city">City</option>
                        </select>
                    </div>
                </div>

                <div class="universities-grid" id="universitiesGrid">
                    <!-- University Card 1 - University of Punjab -->
                    <div class="university-card" data-city="lahore" data-program="computer-science" data-deadline="urgent" data-fee="50000" data-type="public">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/1e40af/ffffff?text=PU" alt="PU" class="uni-logo">
                            <div class="uni-info">
                                <h3>University of Punjab</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Lahore, Punjab</p>
                                <div class="uni-badges">
                                    <span class="uni-type public">Public</span>
                                    <span class="ranking">#3 in Pakistan</span>
                                </div>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <span>4.2</span>
                                </div>
                            </div>
                            <button class="bookmark-btn" data-university="pu">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag cs">Computer Science</span>
                                <span class="program-tag eng">Engineering</span>
                                <span class="program-tag bus">Business</span>
                                <span class="program-tag more">+12 more</span>
                            </div>
                            <p class="uni-description">One of Pakistan's oldest and most prestigious universities offering diverse academic programs with excellent research facilities.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text urgent">Deadline: Aug 15, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: ₨50,000/year</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-users"></i>
                                    <span>45,000+ Students</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="viewUniversity('pu')">View Details</button>
                            <button class="btn btn-primary btn-sm" onclick="applyNow('pu')">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 2 - University of Karachi -->
                    <div class="university-card" data-city="karachi" data-program="business" data-deadline="soon" data-fee="45000" data-type="public">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/059669/ffffff?text=KU" alt="KU" class="uni-logo">
                            <div class="uni-info">
                                <h3>University of Karachi</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Karachi, Sindh</p>
                                <div class="uni-badges">
                                    <span class="uni-type public">Public</span>
                                    <span class="ranking">#5 in Pakistan</span>
                                </div>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.5</span>
                                </div>
                            </div>
                            <button class="bookmark-btn" data-university="ku">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag bus">Business Administration</span>
                                <span class="program-tag med">Medicine</span>
                                <span class="program-tag arts">Arts</span>
                                <span class="program-tag more">+18 more</span>
                            </div>
                            <p class="uni-description">Leading public university in Karachi with excellent research facilities and diverse academic programs across multiple disciplines.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text warning">Deadline: Aug 30, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: ₨45,000/year</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-users"></i>
                                    <span>52,000+ Students</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="viewUniversity('ku')">View Details</button>
                            <button class="btn btn-primary btn-sm" onclick="applyNow('ku')">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 3 - LUMS -->
                    <div class="university-card featured" data-city="lahore" data-program="business" data-deadline="open" data-fee="800000" data-type="private">
                        <div class="featured-badge">
                            <i class="fas fa-crown"></i>
                            <span>Featured</span>
                        </div>
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/dc2626/ffffff?text=LUMS" alt="LUMS" class="uni-logo">
                            <div class="uni-info">
                                <h3>LUMS</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Lahore, Punjab</p>
                                <div class="uni-badges">
                                    <span class="uni-type private">Private</span>
                                    <span class="ranking">#1 in Pakistan</span>
                                </div>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                </div>
                            </div>
                            <button class="bookmark-btn bookmarked" data-university="lums">
                                <i class="fas fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag bus">MBA</span>
                                <span class="program-tag cs">Computer Science</span>
                                <span class="program-tag eco">Economics</span>
                                <span class="program-tag more">+8 more</span>
                            </div>
                            <p class="uni-description">Premier private university known for business education, research excellence, and world-class faculty with international recognition.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text normal">Deadline: Oct 1, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>SAT/LUMS Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: ₨800,000/year</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-users"></i>
                                    <span>5,500+ Students</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-award"></i>
                                    <span>95% Job Placement</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="viewUniversity('lums')">View Details</button>
                            <button class="btn btn-primary btn-sm" onclick="applyNow('lums')">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 4 - NUST -->
                    <div class="university-card" data-city="islamabad" data-program="engineering" data-deadline="open" data-fee="350000" data-type="public">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/7c3aed/ffffff?text=NUST" alt="NUST" class="uni-logo">
                            <div class="uni-info">
                                <h3>NUST</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Islamabad</p>
                                <div class="uni-badges">
                                    <span class="uni-type public">Public</span>
                                    <span class="ranking">#2 in Pakistan</span>
                                </div>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.7</span>
                                </div>
                            </div>
                            <button class="bookmark-btn" data-university="nust">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag eng">Engineering</span>
                                <span class="program-tag cs">Computer Science</span>
                                <span class="program-tag bus">Management</span>
                                <span class="program-tag more">+15 more</span>
                            </div>
                            <p class="uni-description">Top-ranked engineering university with state-of-the-art facilities, cutting-edge research programs, and industry partnerships.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text normal">Deadline: Sep 20, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>NET Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: ₨350,000/year</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-users"></i>
                                    <span>15,000+ Students</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-award"></i>
                                    <span>92% Job Placement</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="viewUniversity('nust')">View Details</button>
                            <button class="btn btn-primary btn-sm" onclick="applyNow('nust')">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 5 - Aga Khan University -->
                    <div class="university-card" data-city="karachi" data-program="medicine" data-deadline="urgent" data-fee="1500000" data-type="private">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/059669/ffffff?text=AKU" alt="AKU" class="uni-logo">
                            <div class="uni-info">
                                <h3>Aga Khan University</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Karachi, Sindh</p>
                                <div class="uni-badges">
                                    <span class="uni-type private">Private</span>
                                    <span class="ranking">#1 Medical</span>
                                </div>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.9</span>
                                </div>
                            </div>
                            <button class="bookmark-btn" data-university="aku">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag med">Medicine</span>
                                <span class="program-tag nurs">Nursing</span>
                                <span class="program-tag edu">Education</span>
                                <span class="program-tag more">+6 more</span>
                            </div>
                            <p class="uni-description">Leading private medical university with international accreditation and world-class healthcare education programs.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text urgent">Deadline: Aug 12, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>AKU Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: ₨1,500,000/year</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-users"></i>
                                    <span>2,800+ Students</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-award"></i>
                                    <span>International Accreditation</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="viewUniversity('aku')">View Details</button>
                            <button class="btn btn-primary btn-sm" onclick="applyNow('aku')">Apply Now</button>
                        </div>
                    </div>

                    <!-- University Card 6 - COMSATS -->
                    <div class="university-card" data-city="islamabad" data-program="computer-science" data-deadline="open" data-fee="180000" data-type="public">
                        <div class="card-header">
                            <img src="https://via.placeholder.com/80x80/1e40af/ffffff?text=CUI" alt="COMSATS" class="uni-logo">
                            <div class="uni-info">
                                <h3>COMSATS University</h3>
                                <p><i class="fas fa-map-marker-alt"></i> Islamabad</p>
                                <div class="uni-badges">
                                    <span class="uni-type public">Public</span>
                                    <span class="ranking">#4 in Pakistan</span>
                                </div>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <span>4.3</span>
                                </div>
                            </div>
                            <button class="bookmark-btn" data-university="comsats">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="programs">
                                <span class="program-tag cs">Computer Science</span>
                                <span class="program-tag eng">Engineering</span>
                                <span class="program-tag bus">Business</span>
                                <span class="program-tag more">+20 more</span>
                            </div>
                            <p class="uni-description">Leading technology university with multiple campuses across Pakistan, known for IT and engineering excellence.</p>
                            <div class="card-features">
                                <div class="feature">
                                    <i class="fas fa-clock"></i>
                                    <span class="deadline-text normal">Deadline: Sep 30, 2024</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-brain"></i>
                                    <span>Entry Test Required</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Fee: ₨180,000/year</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-users"></i>
                                    <span>35,000+ Students</span>
                                </div>
                                <div class="feature">
                                    <i class="fas fa-building"></i>
                                    <span>7 Campuses</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline btn-sm" onclick="viewUniversity('comsats')">View Details</button>
                            <button class="btn btn-primary btn-sm" onclick="applyNow('comsats')">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Load More Button -->
                <div class="load-more-section">
                    <button class="btn btn-outline btn-large" id="loadMoreBtn">
                        <i class="fas fa-plus"></i>
                        Load More Universities
                    </button>
                </div>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        // Mock data for universities
        const universitiesData = [
            {
                id: 'pu',
                name: 'University of Punjab',
                city: 'lahore',
                type: 'public',
                programs: ['computer-science', 'engineering', 'business'],
                fee: 50000,
                deadline: 'urgent',
                rating: 4.2,
                students: 45000,
                ranking: '#3 in Pakistan'
            },
            {
                id: 'ku',
                name: 'University of Karachi',
                city: 'karachi',
                type: 'public',
                programs: ['business', 'medicine', 'arts'],
                fee: 45000,
                deadline: 'soon',
                rating: 4.5,
                students: 52000,
                ranking: '#5 in Pakistan'
            },
            {
                id: 'lums',
                name: 'LUMS',
                city: 'lahore',
                type: 'private',
                programs: ['business', 'computer-science', 'economics'],
                fee: 800000,
                deadline: 'open',
                rating: 4.8,
                students: 5500,
                ranking: '#1 in Pakistan',
                featured: true
            },
            {
                id: 'nust',
                name: 'NUST',
                city: 'islamabad',
                type: 'public',
                programs: ['engineering', 'computer-science', 'business'],
                fee: 350000,
                deadline: 'open',
                rating: 4.7,
                students: 15000,
                ranking: '#2 in Pakistan'
            },
            {
                id: 'aku',
                name: 'Aga Khan University',
                city: 'karachi',
                type: 'private',
                programs: ['medicine', 'nursing', 'education'],
                fee: 1500000,
                deadline: 'urgent',
                rating: 4.9,
                students: 2800,
                ranking: '#1 Medical'
            },
            {
                id: 'comsats',
                name: 'COMSATS University',
                city: 'islamabad',
                type: 'public',
                programs: ['computer-science', 'engineering', 'business'],
                fee: 180000,
                deadline: 'open',
                rating: 4.3,
                students: 35000,
                ranking: '#4 in Pakistan'
            }
        ];

        // Universities page functionality
        function initializeUniversitiesList() {
            // Filter functionality
            const filters = {
                city: document.getElementById('cityFilter'),
                program: document.getElementById('programFilter'),
                fee: document.getElementById('feeFilter'),
                deadline: document.getElementById('deadlineFilter'),
                test: document.getElementById('testFilter'),
                type: document.getElementById('typeFilter')
            };

            Object.values(filters).forEach(filter => {
                filter.addEventListener('change', applyFilters);
            });

            // Clear filters
            document.querySelector('.clear-filters').addEventListener('click', clearFilters);

            // Sort functionality
            document.getElementById('sortBy').addEventListener('change', sortUniversities);

            // Bookmark functionality
            document.querySelectorAll('.bookmark-btn').forEach(btn => {
                btn.addEventListener('click', toggleBookmark);
            });

            // Load more functionality
            document.getElementById('loadMoreBtn').addEventListener('click', loadMoreUniversities);

            // Initialize saved bookmarks
            loadSavedBookmarks();
        }

        function applyFilters() {
            const cards = document.querySelectorAll('.university-card');
            const cityFilter = document.getElementById('cityFilter').value;
            const programFilter = document.getElementById('programFilter').value;
            const feeFilter = document.getElementById('feeFilter').value;
            const deadlineFilter = document.getElementById('deadlineFilter').value;
            const testFilter = document.getElementById('testFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            let visibleCount = 0;

            cards.forEach(card => {
                let show = true;

                // City filter
                if (cityFilter && card.dataset.city !== cityFilter) {
                    show = false;
                }

                // Program filter
                if (programFilter && !card.dataset.program.includes(programFilter)) {
                    show = false;
                }

                // Fee filter
                if (feeFilter && show) {
                    const fee = parseInt(card.dataset.fee);
                    const [min, max] = feeFilter.split('-').map(f => f.replace('+', ''));
                    if (max) {
                        show = fee >= parseInt(min) && fee <= parseInt(max);
                    } else {
                        show = fee >= parseInt(min);
                    }
                }

                // Deadline filter
                if (deadlineFilter && card.dataset.deadline !== deadlineFilter) {
                    show = false;
                }

                // Type filter
                if (typeFilter && card.dataset.type !== typeFilter) {
                    show = false;
                }

                if (show) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // Update results count
            const resultsInfo = document.querySelector('.results-info');
            if (resultsInfo) {
                resultsInfo.textContent = `Showing ${visibleCount} universities`;
            }

            // Show empty state if no results
            if (visibleCount === 0) {
                showEmptyState();
            } else {
                hideEmptyState();
            }
        }

        function clearFilters() {
            document.getElementById('cityFilter').value = '';
            document.getElementById('programFilter').value = '';
            document.getElementById('feeFilter').value = '';
            document.getElementById('deadlineFilter').value = '';
            document.getElementById('testFilter').value = '';
            document.getElementById('typeFilter').value = '';

            document.querySelectorAll('.university-card').forEach(card => {
                card.style.display = 'block';
            });

            hideEmptyState();
            const resultsInfo = document.querySelector('.results-info');
            if (resultsInfo) {
                resultsInfo.textContent = 'Showing 6 of 150+ universities';
            }
            showNotification('Filters cleared', 'info');
        }

        function sortUniversities() {
            const sortBy = document.getElementById('sortBy').value;
            const grid = document.getElementById('universitiesGrid');
            const cards = Array.from(grid.children);

            cards.sort((a, b) => {
                switch (sortBy) {
                    case 'name':
                        return a.querySelector('h3').textContent.localeCompare(b.querySelector('h3').textContent);
                    case 'city':
                        return a.dataset.city.localeCompare(b.dataset.city);
                    case 'deadline':
                        const deadlineOrder = { urgent: 1, soon: 2, open: 3 };
                        return deadlineOrder[a.dataset.deadline] - deadlineOrder[b.dataset.deadline];
                    default:
                        return 0;
                }
            });

            cards.forEach(card => grid.appendChild(card));
        }

        function toggleBookmark(event) {
            event.preventDefault();
            const icon = this.querySelector('i');
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                showNotification('University saved to bookmarks!', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                showNotification('University removed from bookmarks!', 'info');
            }
        }

        function loadMoreUniversities() {
            showNotification('Loading more universities...', 'info');
            // Simulate loading delay
            setTimeout(() => {
                showNotification('More universities loaded!', 'success');
            }, 1500);
        }

        // Additional functions for enhanced functionality
        function saveFilters() {
            const filters = {
                city: document.getElementById('cityFilter').value,
                program: document.getElementById('programFilter').value,
                fee: document.getElementById('feeFilter').value,
                deadline: document.getElementById('deadlineFilter').value,
                test: document.getElementById('testFilter').value,
                type: document.getElementById('typeFilter').value
            };

            localStorage.setItem('savedFilters', JSON.stringify(filters));
            showNotification('Filters saved successfully!', 'success');
        }

        function viewUniversity(universityId) {
            showNotification(`Loading ${universityId.toUpperCase()} details...`, 'info');
            setTimeout(() => {
                window.location.href = `university-details.html?id=${universityId}`;
            }, 1000);
        }

        function applyNow(universityId) {
            const university = universitiesData.find(u => u.id === universityId);
            if (university) {
                showNotification(`Starting application for ${university.name}...`, 'info');
                setTimeout(() => {
                    window.location.href = `apply.html?university=${universityId}`;
                }, 1000);
            }
        }

        function toggleBookmark(event) {
            event.preventDefault();
            const btn = event.currentTarget;
            const universityId = btn.dataset.university;
            const icon = btn.querySelector('i');

            let bookmarks = JSON.parse(localStorage.getItem('bookmarkedUniversities') || '[]');

            if (bookmarks.includes(universityId)) {
                // Remove bookmark
                bookmarks = bookmarks.filter(id => id !== universityId);
                icon.className = 'far fa-bookmark';
                btn.classList.remove('bookmarked');
                showNotification('University removed from bookmarks', 'info');
            } else {
                // Add bookmark
                bookmarks.push(universityId);
                icon.className = 'fas fa-bookmark';
                btn.classList.add('bookmarked');
                showNotification('University bookmarked successfully!', 'success');
            }

            localStorage.setItem('bookmarkedUniversities', JSON.stringify(bookmarks));
        }

        function loadSavedBookmarks() {
            const bookmarks = JSON.parse(localStorage.getItem('bookmarkedUniversities') || '[]');
            bookmarks.forEach(universityId => {
                const btn = document.querySelector(`[data-university="${universityId}"]`);
                if (btn) {
                    const icon = btn.querySelector('i');
                    icon.className = 'fas fa-bookmark';
                    btn.classList.add('bookmarked');
                }
            });
        }

        function showEmptyState() {
            let emptyState = document.querySelector('.empty-state');
            if (!emptyState) {
                emptyState = document.createElement('div');
                emptyState.className = 'empty-state';
                emptyState.innerHTML = `
                    <div class="empty-state-content">
                        <i class="fas fa-search"></i>
                        <h3>No universities found</h3>
                        <p>Try adjusting your filters to see more results</p>
                        <button class="btn btn-primary" onclick="clearFilters()">Clear Filters</button>
                    </div>
                `;
                document.querySelector('.universities-grid').appendChild(emptyState);
            }
            emptyState.style.display = 'flex';
        }

        function hideEmptyState() {
            const emptyState = document.querySelector('.empty-state');
            if (emptyState) {
                emptyState.style.display = 'none';
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeUniversitiesList();
            loadSavedBookmarks();
        });
    </script>
</body>
</html>
