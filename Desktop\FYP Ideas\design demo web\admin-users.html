<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - UniConnect Pakistan Admin</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="admin-users">
    <!-- Admin Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <div class="admin-logo">
                <i class="fas fa-shield-alt"></i>
                <span>UniConnect Admin</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="admin-users.html" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                        <span class="nav-badge">1,245</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-universities.html" class="nav-link">
                        <i class="fas fa-university"></i>
                        <span>Universities</span>
                        <span class="nav-badge">150</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-applications.html" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        <span>Applications</span>
                        <span class="nav-badge">3,456</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-tests.html" class="nav-link">
                        <i class="fas fa-brain"></i>
                        <span>Mock Tests</span>
                        <span class="nav-badge">89</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-analytics.html" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-notifications.html" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                        <span class="nav-badge new">12</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="admin-settings.html" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="admin-profile">
                <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=A" alt="Admin" class="admin-avatar">
                <div class="admin-info">
                    <span class="admin-name">Admin User</span>
                    <span class="admin-role">Super Admin</span>
                </div>
            </div>
            <button class="logout-btn" onclick="adminLogout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </aside>

    <!-- Main Admin Content -->
    <main class="admin-main">
        <!-- Admin Header -->
        <header class="admin-header">
            <div class="header-left">
                <h1>User Management</h1>
                <p>Manage all registered users and their accounts</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="action-btn" onclick="exportUsers()">
                        <i class="fas fa-download"></i>
                        Export Users
                    </button>
                    <button class="action-btn primary" onclick="openAddUserModal()">
                        <i class="fas fa-user-plus"></i>
                        Add New User
                    </button>
                </div>
            </div>
        </header>

        <!-- User Stats -->
        <section class="user-stats">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon total">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Total Users</h3>
                        <div class="stat-number">1,245</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12% this month
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon active">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Active Users</h3>
                        <div class="stat-number">1,089</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8% this month
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon new">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stat-content">
                        <h3>New This Month</h3>
                        <div class="stat-number">156</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +25% vs last month
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon verified">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Verified Users</h3>
                        <div class="stat-number">987</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            79% verification rate
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Filters and Search -->
        <section class="user-filters">
            <div class="filters-container">
                <div class="search-section">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search users by name, email, or phone..." id="userSearch">
                    </div>
                </div>
                
                <div class="filter-section">
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                    </select>
                    
                    <select id="verificationFilter" class="filter-select">
                        <option value="">All Verification</option>
                        <option value="verified">Verified</option>
                        <option value="unverified">Unverified</option>
                    </select>
                    
                    <select id="roleFilter" class="filter-select">
                        <option value="">All Roles</option>
                        <option value="student">Student</option>
                        <option value="admin">Admin</option>
                        <option value="moderator">Moderator</option>
                    </select>
                    
                    <button class="filter-btn" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                </div>
            </div>
        </section>

        <!-- Users Table -->
        <section class="users-table-section">
            <div class="table-container">
                <div class="table-header">
                    <h3>All Users</h3>
                    <div class="table-actions">
                        <button class="table-action-btn" onclick="selectAllUsers()">
                            <i class="fas fa-check-square"></i>
                            Select All
                        </button>
                        <button class="table-action-btn" onclick="bulkAction('suspend')">
                            <i class="fas fa-ban"></i>
                            Bulk Suspend
                        </button>
                        <button class="table-action-btn" onclick="bulkAction('delete')">
                            <i class="fas fa-trash"></i>
                            Bulk Delete
                        </button>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll"></th>
                                <th>User</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Status</th>
                                <th>Verification</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td><input type="checkbox" class="user-checkbox" data-user-id="1"></td>
                                <td>
                                    <div class="user-info">
                                        <img src="https://via.placeholder.com/40x40/3b82f6/ffffff?text=AK" alt="User" class="user-avatar">
                                        <div class="user-details">
                                            <span class="user-name">Ahmad Khan</span>
                                            <span class="user-id">#USR001</span>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+92 300 1234567</td>
                                <td><span class="status-badge active">Active</span></td>
                                <td><span class="verification-badge verified">Verified</span></td>
                                <td>Aug 1, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" onclick="viewUser(1)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" onclick="editUser(1)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteUser(1)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td><input type="checkbox" class="user-checkbox" data-user-id="2"></td>
                                <td>
                                    <div class="user-info">
                                        <img src="https://via.placeholder.com/40x40/10b981/ffffff?text=SA" alt="User" class="user-avatar">
                                        <div class="user-details">
                                            <span class="user-name">Sarah Ahmed</span>
                                            <span class="user-id">#USR002</span>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+92 301 2345678</td>
                                <td><span class="status-badge active">Active</span></td>
                                <td><span class="verification-badge verified">Verified</span></td>
                                <td>Aug 2, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" onclick="viewUser(2)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" onclick="editUser(2)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteUser(2)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td><input type="checkbox" class="user-checkbox" data-user-id="3"></td>
                                <td>
                                    <div class="user-info">
                                        <img src="https://via.placeholder.com/40x40/f59e0b/ffffff?text=AH" alt="User" class="user-avatar">
                                        <div class="user-details">
                                            <span class="user-name">Ali Hassan</span>
                                            <span class="user-id">#USR003</span>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+92 302 3456789</td>
                                <td><span class="status-badge inactive">Inactive</span></td>
                                <td><span class="verification-badge unverified">Unverified</span></td>
                                <td>Aug 3, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" onclick="viewUser(3)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" onclick="editUser(3)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteUser(3)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            
                            <tr>
                                <td><input type="checkbox" class="user-checkbox" data-user-id="4"></td>
                                <td>
                                    <div class="user-info">
                                        <img src="https://via.placeholder.com/40x40/ef4444/ffffff?text=FK" alt="User" class="user-avatar">
                                        <div class="user-details">
                                            <span class="user-name">Fatima Khan</span>
                                            <span class="user-id">#USR004</span>
                                        </div>
                                    </div>
                                </td>
                                <td><EMAIL></td>
                                <td>+92 303 4567890</td>
                                <td><span class="status-badge suspended">Suspended</span></td>
                                <td><span class="verification-badge verified">Verified</span></td>
                                <td>Aug 4, 2024</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" onclick="viewUser(4)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" onclick="editUser(4)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" onclick="deleteUser(4)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="table-pagination">
                    <div class="pagination-info">
                        Showing 1-4 of 1,245 users
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">...</button>
                        <button class="pagination-btn">312</button>
                        <button class="pagination-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Add User Modal -->
    <div class="modal" id="addUserModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>Add New User</h3>
                <button class="close-modal" onclick="closeModal('addUserModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addUserForm" class="user-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="role">Role</label>
                            <select id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="student">Student</option>
                                <option value="admin">Admin</option>
                                <option value="moderator">Moderator</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-container">
                            <input type="checkbox" name="sendWelcomeEmail">
                            <span class="checkmark"></span>
                            Send welcome email to user
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal('addUserModal')">Cancel</button>
                <button class="btn btn-primary" onclick="saveUser()">Add User</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        function toggleSidebar() {
            document.querySelector('.admin-sidebar').classList.toggle('collapsed');
        }

        function adminLogout() {
            if (confirm('Are you sure you want to logout?')) {
                showNotification('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'admin-login.html';
                }, 1500);
            }
        }

        function exportUsers() {
            showNotification('Exporting user data...', 'info');
            setTimeout(() => {
                showNotification('User data exported successfully!', 'success');
            }, 2000);
        }

        function openAddUserModal() {
            document.getElementById('addUserModal').classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        function saveUser() {
            const form = document.getElementById('addUserForm');
            if (validateForm('addUserForm')) {
                showNotification('Adding new user...', 'info');
                setTimeout(() => {
                    showNotification('User added successfully!', 'success');
                    closeModal('addUserModal');
                    form.reset();
                }, 2000);
            }
        }

        function viewUser(userId) {
            showNotification(`Viewing user #${userId}`, 'info');
        }

        function editUser(userId) {
            showNotification(`Editing user #${userId}`, 'info');
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                showNotification(`User #${userId} deleted successfully!`, 'success');
            }
        }

        function selectAllUsers() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            const selectAll = document.getElementById('selectAll');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function bulkAction(action) {
            const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
            if (selectedUsers.length === 0) {
                showNotification('Please select users first', 'warning');
                return;
            }
            
            if (confirm(`Are you sure you want to ${action} ${selectedUsers.length} users?`)) {
                showNotification(`Bulk ${action} completed successfully!`, 'success');
            }
        }

        function clearFilters() {
            document.getElementById('userSearch').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('verificationFilter').value = '';
            document.getElementById('roleFilter').value = '';
            showNotification('Filters cleared', 'info');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Select all functionality
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.user-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        });
    </script>
</body>
</html>
