<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="notifications">
    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="logo">
                <a href="dashboard.html">
                    <i class="fas fa-university"></i>
                    <span>UniConnect Pakistan</span>
                </a>
            </div>
            <div class="nav-center">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search notifications..." id="searchInput">
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon active" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </button>
                    <a href="chatbot.html" class="nav-icon" data-tooltip="AI Assistant">
                        <i class="fas fa-robot"></i>
                    </a>
                    <div class="profile-dropdown">
                        <button class="profile-btn">
                            <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=U" alt="Profile" class="profile-avatar">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="documents.html"><i class="fas fa-file-alt"></i> Documents</a>
                            <a href="#"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="index.html"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="notifications-main">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="header-content">
                    <div class="breadcrumb">
                        <a href="dashboard.html">Dashboard</a>
                        <i class="fas fa-chevron-right"></i>
                        <span>Notifications</span>
                    </div>
                    <h1>Notifications</h1>
                    <p>Stay updated with important deadlines and announcements</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-outline" onclick="markAllAsRead()">
                        <i class="fas fa-check-double"></i>
                        Mark All as Read
                    </button>
                    <button class="btn btn-primary" onclick="openNotificationSettings()">
                        <i class="fas fa-cog"></i>
                        Settings
                    </button>
                </div>
            </div>
        </section>

        <!-- Notification Filters -->
        <section class="notification-filters">
            <div class="container">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">
                        All <span class="count">8</span>
                    </button>
                    <button class="filter-tab" data-filter="unread">
                        Unread <span class="count">3</span>
                    </button>
                    <button class="filter-tab" data-filter="deadlines">
                        Deadlines <span class="count">2</span>
                    </button>
                    <button class="filter-tab" data-filter="tests">
                        Tests <span class="count">2</span>
                    </button>
                    <button class="filter-tab" data-filter="applications">
                        Applications <span class="count">1</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- Notifications List -->
        <section class="notifications-list">
            <div class="container">
                <!-- Urgent Notifications -->
                <div class="notification-group">
                    <h3 class="group-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Urgent
                    </h3>
                    
                    <div class="notification-item unread urgent" data-category="deadlines">
                        <div class="notification-icon urgent">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Admission Deadline Approaching</h4>
                                <span class="notification-time">2 hours ago</span>
                            </div>
                            <p>University of Punjab Computer Science program deadline is in 2 days (August 15, 2024). Complete your application now!</p>
                            <div class="notification-actions">
                                <button class="action-btn primary">Apply Now</button>
                                <button class="action-btn secondary">View Details</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsRead(this)">Mark as Read</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>

                    <div class="notification-item unread urgent" data-category="tests">
                        <div class="notification-icon urgent">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>MDCAT Mock Test Starting Soon</h4>
                                <span class="notification-time">4 hours ago</span>
                            </div>
                            <p>Your scheduled MDCAT mock test will start in 30 minutes. Make sure you're ready!</p>
                            <div class="notification-actions">
                                <button class="action-btn primary">Start Test</button>
                                <button class="action-btn secondary">Reschedule</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsRead(this)">Mark as Read</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Notifications -->
                <div class="notification-group">
                    <h3 class="group-title">
                        <i class="fas fa-calendar-day"></i>
                        Today
                    </h3>

                    <div class="notification-item unread" data-category="applications">
                        <div class="notification-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Application Submitted Successfully</h4>
                                <span class="notification-time">6 hours ago</span>
                            </div>
                            <p>Your application to LUMS MBA program has been submitted successfully. Application ID: #LMS2024-5678</p>
                            <div class="notification-actions">
                                <button class="action-btn secondary">View Application</button>
                                <button class="action-btn secondary">Download Receipt</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsRead(this)">Mark as Read</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>

                    <div class="notification-item read" data-category="tests">
                        <div class="notification-icon info">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>New Mock Test Available</h4>
                                <span class="notification-time">8 hours ago</span>
                            </div>
                            <p>A new ECAT practice test is now available. Test your engineering knowledge and improve your scores.</p>
                            <div class="notification-actions">
                                <button class="action-btn primary">Take Test</button>
                                <button class="action-btn secondary">View Syllabus</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsUnread(this)">Mark as Unread</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Yesterday's Notifications -->
                <div class="notification-group">
                    <h3 class="group-title">
                        <i class="fas fa-calendar-minus"></i>
                        Yesterday
                    </h3>

                    <div class="notification-item read" data-category="deadlines">
                        <div class="notification-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Document Verification Required</h4>
                                <span class="notification-time">1 day ago</span>
                            </div>
                            <p>Your uploaded documents for Karachi University need verification. Please ensure all documents are clear and complete.</p>
                            <div class="notification-actions">
                                <button class="action-btn primary">Upload Documents</button>
                                <button class="action-btn secondary">View Requirements</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsUnread(this)">Mark as Unread</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>

                    <div class="notification-item read" data-category="applications">
                        <div class="notification-icon info">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>New University Added</h4>
                                <span class="notification-time">1 day ago</span>
                            </div>
                            <p>Comsats University Islamabad has been added to our platform. Explore their programs and admission requirements.</p>
                            <div class="notification-actions">
                                <button class="action-btn secondary">Explore Programs</button>
                                <button class="action-btn secondary">View Details</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsUnread(this)">Mark as Unread</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Older Notifications -->
                <div class="notification-group">
                    <h3 class="group-title">
                        <i class="fas fa-history"></i>
                        Earlier
                    </h3>

                    <div class="notification-item read" data-category="tests">
                        <div class="notification-icon success">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Great Test Performance!</h4>
                                <span class="notification-time">3 days ago</span>
                            </div>
                            <p>Congratulations! You scored 85% in your recent MDCAT mock test. You're in the top 15% of test takers.</p>
                            <div class="notification-actions">
                                <button class="action-btn secondary">View Report</button>
                                <button class="action-btn secondary">Take Another Test</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsUnread(this)">Mark as Unread</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>

                    <div class="notification-item read" data-category="applications">
                        <div class="notification-icon info">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Welcome to UniConnect Pakistan!</h4>
                                <span class="notification-time">1 week ago</span>
                            </div>
                            <p>Welcome to UniConnect Pakistan! Start exploring universities, take mock tests, and begin your admission journey.</p>
                            <div class="notification-actions">
                                <button class="action-btn secondary">Get Started</button>
                                <button class="action-btn secondary">Take Tour</button>
                            </div>
                        </div>
                        <div class="notification-menu">
                            <button class="menu-btn" onclick="toggleNotificationMenu(this)">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="menu-dropdown">
                                <button onclick="markAsUnread(this)">Mark as Unread</button>
                                <button onclick="deleteNotification(this)">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Load More -->
                <div class="load-more-section">
                    <button class="btn btn-outline btn-large" onclick="loadMoreNotifications()">
                        <i class="fas fa-plus"></i>
                        Load More Notifications
                    </button>
                </div>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        function initializeNotifications() {
            // Filter tabs functionality
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    const filter = this.dataset.filter;
                    filterNotifications(filter);
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.notification-menu')) {
                    document.querySelectorAll('.menu-dropdown').forEach(menu => {
                        menu.classList.remove('active');
                    });
                }
            });
        }

        function filterNotifications(filter) {
            const notifications = document.querySelectorAll('.notification-item');
            
            notifications.forEach(notification => {
                if (filter === 'all') {
                    notification.style.display = 'flex';
                } else if (filter === 'unread') {
                    notification.style.display = notification.classList.contains('unread') ? 'flex' : 'none';
                } else {
                    notification.style.display = notification.dataset.category === filter ? 'flex' : 'none';
                }
            });
        }

        function toggleNotificationMenu(button) {
            const dropdown = button.nextElementSibling;
            const isActive = dropdown.classList.contains('active');
            
            // Close all other dropdowns
            document.querySelectorAll('.menu-dropdown').forEach(menu => {
                menu.classList.remove('active');
            });
            
            // Toggle current dropdown
            if (!isActive) {
                dropdown.classList.add('active');
            }
        }

        function markAsRead(button) {
            const notification = button.closest('.notification-item');
            notification.classList.remove('unread');
            notification.classList.add('read');
            updateNotificationCounts();
            showNotification('Notification marked as read', 'success');
        }

        function markAsUnread(button) {
            const notification = button.closest('.notification-item');
            notification.classList.remove('read');
            notification.classList.add('unread');
            updateNotificationCounts();
            showNotification('Notification marked as unread', 'info');
        }

        function deleteNotification(button) {
            if (confirm('Are you sure you want to delete this notification?')) {
                const notification = button.closest('.notification-item');
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => {
                    notification.remove();
                    updateNotificationCounts();
                    showNotification('Notification deleted', 'success');
                }, 300);
            }
        }

        function markAllAsRead() {
            const unreadNotifications = document.querySelectorAll('.notification-item.unread');
            unreadNotifications.forEach(notification => {
                notification.classList.remove('unread');
                notification.classList.add('read');
            });
            updateNotificationCounts();
            showNotification('All notifications marked as read', 'success');
        }

        function updateNotificationCounts() {
            const allCount = document.querySelectorAll('.notification-item').length;
            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
            const deadlineCount = document.querySelectorAll('.notification-item[data-category="deadlines"]').length;
            const testCount = document.querySelectorAll('.notification-item[data-category="tests"]').length;
            const applicationCount = document.querySelectorAll('.notification-item[data-category="applications"]').length;

            document.querySelector('[data-filter="all"] .count').textContent = allCount;
            document.querySelector('[data-filter="unread"] .count').textContent = unreadCount;
            document.querySelector('[data-filter="deadlines"] .count').textContent = deadlineCount;
            document.querySelector('[data-filter="tests"] .count').textContent = testCount;
            document.querySelector('[data-filter="applications"] .count').textContent = applicationCount;

            // Update navbar badge
            const navBadge = document.querySelector('.nav-icon .badge');
            if (navBadge) {
                navBadge.textContent = unreadCount;
                if (unreadCount === 0) {
                    navBadge.style.display = 'none';
                } else {
                    navBadge.style.display = 'block';
                }
            }
        }

        function openNotificationSettings() {
            showNotification('Notification settings coming soon!', 'info');
        }

        function loadMoreNotifications() {
            showNotification('Loading more notifications...', 'info');
            setTimeout(() => {
                showNotification('No more notifications to load', 'info');
            }, 1500);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeNotifications);
    </script>

    <style>
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>
</body>
</html>
