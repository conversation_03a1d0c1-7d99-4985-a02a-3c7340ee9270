<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Management - University Portal</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="university-cms">
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <button class="theme-option light active" onclick="setTheme('light')" title="Light Mode">
            <i class="fas fa-sun"></i>
        </button>
        <button class="theme-option dark" onclick="setTheme('dark')" title="Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <div class="university-layout">
        <!-- University Sidebar -->
        <aside class="university-sidebar">
            <div class="sidebar-header">
                <div class="university-logo">
                    <i class="fas fa-university"></i>
                    <span>University of Punjab</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="university-dashboard.html" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="university-applications.html" class="nav-link">
                            <i class="fas fa-file-alt"></i>
                            <span>Applications</span>
                            <span class="nav-badge">156</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="university-programs.html" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Programs</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="university-cms.html" class="nav-link">
                            <i class="fas fa-edit"></i>
                            <span>Content Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="university-settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="university-profile">
                    <img src="https://via.placeholder.com/40x40/059669/ffffff?text=PU" alt="University" class="university-avatar">
                    <div class="university-info">
                        <span class="university-name">University of Punjab</span>
                        <span class="university-role">Admin Portal</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="universityLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="university-main">
            <!-- Mobile Menu Button -->
            <button class="mobile-menu-btn" onclick="toggleSidebar()" style="display: none;">
                <i class="fas fa-bars"></i>
            </button>
            <!-- Header -->
            <header class="university-header">
                <div class="header-left">
                    <h1>Content Management System</h1>
                    <p>Manage your university's content, programs, and admission information</p>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn" onclick="previewChanges()">
                            <i class="fas fa-eye"></i>
                            Preview
                        </button>
                        <button class="action-btn primary" onclick="publishChanges()">
                            <i class="fas fa-upload"></i>
                            Publish
                        </button>
                    </div>
                </div>
            </header>

            <!-- CMS Content -->
            <div class="cms-container">
                <!-- CMS Tabs -->
                <div class="cms-tabs">
                    <button class="cms-tab active" onclick="switchTab('programs')">
                        <i class="fas fa-graduation-cap"></i>
                        Programs & Intakes
                    </button>
                    <button class="cms-tab" onclick="switchTab('news')">
                        <i class="fas fa-newspaper"></i>
                        Admission News
                    </button>
                    <button class="cms-tab" onclick="switchTab('tests')">
                        <i class="fas fa-brain"></i>
                        Test Content
                    </button>
                    <button class="cms-tab" onclick="switchTab('university')">
                        <i class="fas fa-university"></i>
                        University Info
                    </button>
                </div>

                <!-- Programs & Intakes Tab -->
                <div id="programs-tab" class="cms-tab-content active">
                    <div class="cms-section">
                        <div class="section-header">
                            <h2>Programs & Intakes Management</h2>
                            <button class="btn btn-primary" onclick="addNewProgram()">
                                <i class="fas fa-plus"></i>
                                Add New Program
                            </button>
                        </div>

                        <div class="programs-grid">
                            <!-- Program Card 1 -->
                            <div class="program-card">
                                <div class="program-header">
                                    <h3>Computer Science</h3>
                                    <div class="program-actions">
                                        <button class="action-btn" onclick="editProgram('cs')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn danger" onclick="deleteProgram('cs')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="program-details">
                                    <p><strong>Degree:</strong> Bachelor of Science (BS)</p>
                                    <p><strong>Duration:</strong> 4 Years</p>
                                    <p><strong>Seats:</strong> 120</p>
                                    <p><strong>Fee:</strong> ₨180,000/year</p>
                                    <p><strong>Deadline:</strong> August 30, 2024</p>
                                    <p><strong>Entry Test:</strong> Required</p>
                                </div>
                                <div class="program-status">
                                    <span class="status-badge active">Active</span>
                                    <span class="applications-count">89 Applications</span>
                                </div>
                            </div>

                            <!-- Program Card 2 -->
                            <div class="program-card">
                                <div class="program-header">
                                    <h3>Business Administration</h3>
                                    <div class="program-actions">
                                        <button class="action-btn" onclick="editProgram('mba')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn danger" onclick="deleteProgram('mba')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="program-details">
                                    <p><strong>Degree:</strong> Master of Business Administration (MBA)</p>
                                    <p><strong>Duration:</strong> 2 Years</p>
                                    <p><strong>Seats:</strong> 80</p>
                                    <p><strong>Fee:</strong> ₨350,000/year</p>
                                    <p><strong>Deadline:</strong> September 15, 2024</p>
                                    <p><strong>Entry Test:</strong> Required</p>
                                </div>
                                <div class="program-status">
                                    <span class="status-badge active">Active</span>
                                    <span class="applications-count">67 Applications</span>
                                </div>
                            </div>

                            <!-- Program Card 3 -->
                            <div class="program-card">
                                <div class="program-header">
                                    <h3>Engineering</h3>
                                    <div class="program-actions">
                                        <button class="action-btn" onclick="editProgram('eng')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn danger" onclick="deleteProgram('eng')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="program-details">
                                    <p><strong>Degree:</strong> Bachelor of Engineering (BE)</p>
                                    <p><strong>Duration:</strong> 4 Years</p>
                                    <p><strong>Seats:</strong> 150</p>
                                    <p><strong>Fee:</strong> ₨200,000/year</p>
                                    <p><strong>Deadline:</strong> August 25, 2024</p>
                                    <p><strong>Entry Test:</strong> Required</p>
                                </div>
                                <div class="program-status">
                                    <span class="status-badge active">Active</span>
                                    <span class="applications-count">123 Applications</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Admission News Tab -->
                <div id="news-tab" class="cms-tab-content">
                    <div class="cms-section">
                        <div class="section-header">
                            <h2>Admission News Manager</h2>
                            <button class="btn btn-primary" onclick="createNews()">
                                <i class="fas fa-plus"></i>
                                Create News Post
                            </button>
                        </div>

                        <div class="news-list">
                            <div class="news-item">
                                <div class="news-content">
                                    <h3>Fall 2024 Admission Extended</h3>
                                    <p>Due to high demand, we have extended the admission deadline for Fall 2024 semester...</p>
                                    <div class="news-meta">
                                        <span class="news-date">August 10, 2024</span>
                                        <span class="news-status published">Published</span>
                                    </div>
                                </div>
                                <div class="news-actions">
                                    <button class="action-btn" onclick="editNews('news1')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" onclick="unpublishNews('news1')">
                                        <i class="fas fa-eye-slash"></i>
                                    </button>
                                    <button class="action-btn danger" onclick="deleteNews('news1')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="news-item">
                                <div class="news-content">
                                    <h3>New Scholarship Program Announced</h3>
                                    <p>University of Punjab is pleased to announce merit-based scholarships for deserving students...</p>
                                    <div class="news-meta">
                                        <span class="news-date">August 8, 2024</span>
                                        <span class="news-status draft">Draft</span>
                                    </div>
                                </div>
                                <div class="news-actions">
                                    <button class="action-btn" onclick="editNews('news2')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" onclick="publishNews('news2')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn danger" onclick="deleteNews('news2')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Content Tab -->
                <div id="tests-tab" class="cms-tab-content">
                    <div class="cms-section">
                        <div class="section-header">
                            <h2>Test Content Manager</h2>
                            <div class="section-actions">
                                <button class="btn btn-outline" onclick="importQuestions()">
                                    <i class="fas fa-upload"></i>
                                    Import Questions
                                </button>
                                <button class="btn btn-primary" onclick="addQuestion()">
                                    <i class="fas fa-plus"></i>
                                    Add Question
                                </button>
                            </div>
                        </div>

                        <div class="test-content-grid">
                            <div class="test-chapter">
                                <h3>Mathematics</h3>
                                <div class="chapter-stats">
                                    <span>45 Questions</span>
                                    <span>Last updated: Aug 5, 2024</span>
                                </div>
                                <div class="chapter-actions">
                                    <button class="btn btn-sm btn-outline" onclick="manageChapter('math')">
                                        <i class="fas fa-cog"></i>
                                        Manage
                                    </button>
                                </div>
                            </div>

                            <div class="test-chapter">
                                <h3>Physics</h3>
                                <div class="chapter-stats">
                                    <span>38 Questions</span>
                                    <span>Last updated: Aug 3, 2024</span>
                                </div>
                                <div class="chapter-actions">
                                    <button class="btn btn-sm btn-outline" onclick="manageChapter('physics')">
                                        <i class="fas fa-cog"></i>
                                        Manage
                                    </button>
                                </div>
                            </div>

                            <div class="test-chapter">
                                <h3>English</h3>
                                <div class="chapter-stats">
                                    <span>52 Questions</span>
                                    <span>Last updated: Aug 7, 2024</span>
                                </div>
                                <div class="chapter-actions">
                                    <button class="btn btn-sm btn-outline" onclick="manageChapter('english')">
                                        <i class="fas fa-cog"></i>
                                        Manage
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- University Info Tab -->
                <div id="university-tab" class="cms-tab-content">
                    <div class="cms-section">
                        <div class="section-header">
                            <h2>University Information</h2>
                            <button class="btn btn-primary" onclick="saveUniversityInfo()">
                                <i class="fas fa-save"></i>
                                Save Changes
                            </button>
                        </div>

                        <div class="university-form">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="uniName">University Name</label>
                                    <input type="text" id="uniName" value="University of Punjab" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label for="uniCode">University Code</label>
                                    <input type="text" id="uniCode" value="PU2024" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label for="uniCity">City</label>
                                    <select id="uniCity" class="form-control">
                                        <option value="lahore" selected>Lahore</option>
                                        <option value="karachi">Karachi</option>
                                        <option value="islamabad">Islamabad</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="uniType">University Type</label>
                                    <select id="uniType" class="form-control">
                                        <option value="public" selected>Public</option>
                                        <option value="private">Private</option>
                                        <option value="semi-government">Semi-Government</option>
                                    </select>
                                </div>

                                <div class="form-group full-width">
                                    <label for="uniDescription">Description</label>
                                    <textarea id="uniDescription" rows="4" class="form-control">One of Pakistan's oldest and most prestigious universities offering diverse academic programs with excellent research facilities.</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="uniWebsite">Website</label>
                                    <input type="url" id="uniWebsite" value="https://pu.edu.pk" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label for="uniPhone">Contact Phone</label>
                                    <input type="tel" id="uniPhone" value="+92-42-99231581" class="form-control">
                                </div>

                                <div class="form-group full-width">
                                    <label for="uniAddress">Address</label>
                                    <textarea id="uniAddress" rows="2" class="form-control">Quaid-e-Azam Campus, Lahore, Punjab, Pakistan</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Notification function
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                padding: 16px 20px;
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 12px;
                max-width: 400px;
                border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
            const color = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';

            notification.innerHTML = `
                <i class="fas fa-${icon}" style="color: ${color}; font-size: 18px;"></i>
                <span style="color: #374151; font-weight: 500;">${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px; margin-left: auto;">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => notification.remove(), 5000);
        }
    </script>
    <script>
        // CMS Functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.cms-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.cms-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function addNewProgram() {
            showNotification('Opening program creation form...', 'info');
        }

        function editProgram(programId) {
            showNotification(`Editing program: ${programId}`, 'info');
        }

        function deleteProgram(programId) {
            if (confirm('Are you sure you want to delete this program?')) {
                showNotification(`Program ${programId} deleted successfully!`, 'success');
            }
        }

        function createNews() {
            showNotification('Opening news creation form...', 'info');
        }

        function editNews(newsId) {
            showNotification(`Editing news: ${newsId}`, 'info');
        }

        function publishNews(newsId) {
            showNotification(`News ${newsId} published successfully!`, 'success');
        }

        function unpublishNews(newsId) {
            showNotification(`News ${newsId} unpublished successfully!`, 'info');
        }

        function deleteNews(newsId) {
            if (confirm('Are you sure you want to delete this news?')) {
                showNotification(`News ${newsId} deleted successfully!`, 'success');
            }
        }

        function addQuestion() {
            showNotification('Opening question creation form...', 'info');
        }

        function manageChapter(chapter) {
            showNotification(`Managing ${chapter} chapter...`, 'info');
        }

        function importQuestions() {
            showNotification('Opening question import dialog...', 'info');
        }

        function saveUniversityInfo() {
            showNotification('Saving university information...', 'info');
            setTimeout(() => {
                showNotification('University information saved successfully!', 'success');
            }, 2000);
        }

        function previewChanges() {
            showNotification('Generating preview...', 'info');
        }

        function publishChanges() {
            showNotification('Publishing changes...', 'info');
            setTimeout(() => {
                showNotification('Changes published successfully!', 'success');
            }, 2000);
        }

        function toggleSidebar() {
            document.querySelector('.university-sidebar').classList.toggle('collapsed');
        }

        function universityLogout() {
            if (confirm('Are you sure you want to logout?')) {
                showNotification('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'university-login.html';
                }, 1500);
            }
        }
    </script>
</body>
</html>
