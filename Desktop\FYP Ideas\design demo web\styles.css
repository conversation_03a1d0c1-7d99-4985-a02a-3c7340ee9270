/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Color Variables */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --warning-color: #f97316;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Onboarding Styles */
.onboarding-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.onboarding-slide {
    display: none;
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 40px;
    animation: slideIn 0.5s ease-in-out;
}

.onboarding-slide.active {
    display: block;
}

.slide-illustration {
    font-size: 80px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.onboarding-slide h2 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 15px;
}

.onboarding-slide p {
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.6;
}

.onboarding-nav {
    position: fixed;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 30px;
}

.skip-btn, .next-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skip-btn:hover, .next-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.progress-dots {
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: white;
    transform: scale(1.2);
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;
    box-shadow: var(--shadow);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
}

.logo i {
    font-size: 24px;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.login-btn {
    background: var(--primary-color);
    color: white !important;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    font-size: 20px;
    cursor: pointer;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--bg-light), white);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.hero-content {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 120px 20px 60px;
    max-width: 1200px;
    margin: 0 auto;
    gap: 60px;
}

.hero-text {
    flex: 1;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.highlight {
    color: var(--primary-color);
}

.hero-text p {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-illustration {
    font-size: 200px;
    color: var(--primary-color);
    opacity: 0.8;
    animation: float 3s ease-in-out infinite;
}

/* Features Section */
.features-section {
    padding: 100px 0;
    background: white;
}

.features-section h2 {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 60px;
    color: var(--text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta-section h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-section p {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
}

.btn-large {
    padding: 18px 40px;
    font-size: 18px;
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Authentication Styles */
.auth-container {
    min-height: 100vh;
    display: flex;
    position: relative;
}

.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    z-index: -1;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
}

.auth-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
}

.auth-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding-top: 20px;
}

.auth-header .logo {
    color: white;
    font-size: 24px;
}

.back-home {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.back-home:hover {
    color: white;
}

.auth-form-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: var(--shadow-lg);
    flex: 1;
    max-height: fit-content;
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    background: var(--bg-light);
    border-radius: 12px;
    padding: 4px;
}

.auth-tab {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-secondary);
}

.auth-tab.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

.auth-form h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.auth-subtitle {
    color: var(--text-secondary);
    margin-bottom: 30px;
    font-size: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 15px;
    color: var(--text-secondary);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: white;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input.error {
    border-color: var(--danger-color);
}

.password-toggle {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 5px;
    z-index: 1;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-secondary);
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.forgot-password:hover {
    text-decoration: underline;
}

.btn-full {
    width: 100%;
    margin-bottom: 20px;
}

.divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
    color: var(--text-secondary);
    font-size: 14px;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 0;
}

.divider span {
    background: white;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.social-login {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.btn-social {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 20px;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-primary);
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-social:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-google:hover {
    border-color: #db4437;
    color: #db4437;
}

.btn-facebook:hover {
    border-color: #4267B2;
    color: #4267B2;
}

.guest-option {
    text-align: center;
    margin-top: 20px;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    padding: 12px 30px;
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid var(--secondary-color);
}

.notification-error {
    border-left: 4px solid var(--danger-color);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    margin-left: auto;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Dashboard Styles */
.dashboard-nav {
    background: white;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.dashboard-nav .nav-container {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    max-width: 1400px;
    margin: 0 auto;
    gap: 30px;
}

.nav-center {
    flex: 1;
    max-width: 500px;
}

.search-bar {
    position: relative;
    width: 100%;
}

.search-bar i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-bar input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-icons {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.nav-icon .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.profile-dropdown {
    position: relative;
}

.profile-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.profile-btn:hover {
    background: var(--bg-light);
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    padding: 10px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-menu a:hover {
    background: var(--bg-light);
}

.dropdown-menu hr {
    margin: 10px 0;
    border: none;
    border-top: 1px solid var(--border-color);
}

.dashboard-main {
    padding-top: 0;
}

.welcome-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 60px 0;
}

.welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.welcome-text h1 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
}

.user-name {
    color: var(--accent-color);
}

.welcome-text p {
    font-size: 18px;
    opacity: 0.9;
}

.quick-stats {
    display: flex;
    gap: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 25px;
    border-radius: 16px;
    text-align: center;
    min-width: 120px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 24px;
    margin-bottom: 10px;
    opacity: 0.8;
}

.stat-number {
    display: block;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.8;
}

.urgent-deadlines {
    padding: 60px 0;
    background: var(--bg-light);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: var(--primary-dark);
}

.deadline-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.deadline-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border-left: 4px solid var(--border-color);
}

.deadline-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.deadline-card.urgent {
    border-left-color: var(--danger-color);
}

.deadline-card.warning {
    border-left-color: var(--warning-color);
}

.deadline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.uni-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.uni-logo {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    object-fit: cover;
}

.uni-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.uni-info p {
    color: var(--text-secondary);
    font-size: 14px;
}

.deadline-badge {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.deadline-badge.urgent {
    background: var(--danger-color);
}

.deadline-badge.warning {
    background: var(--warning-color);
}

.deadline-actions {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

.quick-actions {
    padding: 60px 0;
    background: white;
}

.quick-actions h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 40px;
    color: var(--text-primary);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.action-card {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 30px 25px;
    text-align: center;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.action-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.action-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.action-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.action-card p {
    color: var(--text-secondary);
    font-size: 14px;
}

.recommended-section {
    padding: 60px 0;
    background: var(--bg-light);
}

.university-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.university-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.university-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 25px 25px 0;
}

.card-header .uni-logo {
    width: 60px;
    height: 60px;
}

.card-header .uni-info {
    flex: 1;
}

.bookmark-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: color 0.3s ease;
}

.bookmark-btn:hover {
    color: var(--accent-color);
}

.card-content {
    padding: 20px 25px 25px;
}

.program-tag {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 15px;
}

.uni-description {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.deadline {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Tooltip Styles */
.tooltip {
    position: absolute;
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    pointer-events: none;
    white-space: nowrap;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-primary);
}

/* Universities Page Styles */
.universities-main {
    padding-top: 0;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 40px 0;
}

.header-content {
    margin-bottom: 30px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 14px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: white;
}

.breadcrumb i {
    font-size: 12px;
    opacity: 0.6;
}

.page-header h1 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-header p {
    font-size: 18px;
    opacity: 0.9;
}

.header-stats {
    display: flex;
    gap: 40px;
}

.header-stats .stat {
    text-align: center;
}

.header-stats .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.header-stats .stat-label {
    font-size: 14px;
    opacity: 0.8;
}

.filters-section {
    background: white;
    padding: 30px 0;
    border-bottom: 1px solid var(--border-color);
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.filters-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.clear-filters {
    background: none;
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
    transition: color 0.3s ease;
}

.clear-filters:hover {
    color: var(--primary-dark);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.filter-group select {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.universities-list {
    padding: 40px 0;
    background: var(--bg-light);
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.results-info {
    color: var(--text-secondary);
    font-weight: 500;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-options label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.sort-options select {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

.universities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.universities-grid .university-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.universities-grid .university-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.universities-grid .card-header {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
}

.universities-grid .card-header .uni-logo {
    width: 80px;
    height: 80px;
    border-radius: 12px;
}

.universities-grid .card-header .uni-info h3 {
    font-size: 20px;
    margin-bottom: 8px;
}

.universities-grid .card-header .uni-info p {
    color: var(--text-secondary);
    margin-bottom: 10px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 2px;
}

.rating i {
    color: var(--accent-color);
    font-size: 14px;
}

.rating span {
    margin-left: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.universities-grid .card-content {
    padding: 25px;
}

.programs {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.programs .program-tag {
    background: var(--bg-light);
    color: var(--primary-color);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid var(--primary-color);
}

.universities-grid .uni-description {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 20px;
}

.card-features {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.feature i {
    color: var(--text-secondary);
    width: 16px;
}

.deadline-text.urgent {
    color: var(--danger-color);
    font-weight: 600;
}

.deadline-text.warning {
    color: var(--warning-color);
    font-weight: 600;
}

.deadline-text.normal {
    color: var(--text-secondary);
}

.universities-grid .card-actions {
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
}

.universities-grid .card-actions .btn {
    flex: 1;
}

.load-more-section {
    text-align: center;
    margin-top: 40px;
}

.btn-large {
    padding: 15px 40px;
    font-size: 16px;
}

/* Mock Test Page Styles */
.mock-test-main {
    padding-top: 0;
}

.upcoming-tests {
    padding: 40px 0;
    background: white;
}

.upcoming-tests h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.test-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.test-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--shadow);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.test-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.test-card.live {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), white);
}

.test-card.upcoming {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), white);
}

.test-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.test-info h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.test-info p {
    color: var(--text-secondary);
    font-size: 14px;
}

.test-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.test-status.live {
    background: var(--secondary-color);
    animation: pulse 2s infinite;
}

.test-status.upcoming {
    background: var(--primary-color);
}

.test-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 25px;
}

.test-details .detail {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--text-secondary);
}

.test-details .detail i {
    width: 16px;
    color: var(--primary-color);
}

.test-actions .btn-full {
    width: 100%;
}

.test-categories {
    padding: 60px 0;
    background: var(--bg-light);
}

.test-categories h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 40px;
    color: var(--text-primary);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.category-card {
    background: white;
    border-radius: 16px;
    padding: 30px 25px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.category-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.category-card h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.category-card p {
    color: var(--text-secondary);
    margin-bottom: 15px;
    font-size: 14px;
}

.category-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    font-size: 12px;
    color: var(--text-secondary);
}

.recent-attempts {
    padding: 60px 0;
    background: white;
}

.attempts-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.attempt-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 25px;
    transition: all 0.3s ease;
}

.attempt-card:hover {
    box-shadow: var(--shadow);
    border-color: var(--primary-color);
}

.attempt-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.attempt-info .test-details h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.attempt-info .test-details p {
    color: var(--text-secondary);
    font-size: 14px;
}

.score-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 20px;
    border-radius: 12px;
    color: white;
    min-width: 80px;
}

.score-badge.excellent {
    background: var(--secondary-color);
}

.score-badge.good {
    background: var(--primary-color);
}

.score-badge.average {
    background: var(--warning-color);
}

.score-badge .score {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 2px;
}

.score-badge .label {
    font-size: 12px;
    opacity: 0.9;
}

.attempt-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
}

.attempt-stats .stat {
    text-align: center;
}

.attempt-stats .stat .value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.attempt-stats .stat .label {
    font-size: 12px;
    color: var(--text-secondary);
}

.attempt-actions {
    display: flex;
    gap: 15px;
}

.performance-section {
    padding: 60px 0;
    background: var(--bg-light);
}

.performance-section h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 40px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.analytics-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.analytics-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--text-primary);
}

.subject-scores {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.subject-score {
    display: flex;
    align-items: center;
    gap: 15px;
}

.subject-score .subject {
    min-width: 80px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
}

.score-bar {
    flex: 1;
    height: 8px;
    background: var(--bg-light);
    border-radius: 4px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.subject-score .percentage {
    min-width: 40px;
    text-align: right;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.trend-chart {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    text-align: center;
    color: var(--text-secondary);
}

.chart-placeholder i {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.chart-placeholder p {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.chart-placeholder span {
    font-size: 14px;
    color: var(--secondary-color);
    font-weight: 600;
}

.strengths-weaknesses {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.strength-item,
.weakness-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
}

.strength-item i {
    color: var(--secondary-color);
}

.weakness-item i {
    color: var(--warning-color);
}

.strength-item span,
.weakness-item span {
    color: var(--text-primary);
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Chatbot Page Styles */
.chatbot-main {
    height: calc(100vh - 80px);
    display: flex;
    position: relative;
}

.chatbot-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    max-width: 800px;
    margin: 0 auto;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: white;
}

.bot-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bot-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.bot-details h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.bot-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-color);
    animation: pulse 2s infinite;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    background: var(--bg-light);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px 25px;
    background: var(--bg-light);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.bot-message {
    align-self: flex-start;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    font-size: 14px;
}

.user-message .message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.user-message .message-content {
    align-items: flex-end;
}

.message-bubble {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: var(--shadow);
    position: relative;
}

.user-message .message-bubble {
    background: var(--primary-color);
    color: white;
}

.message-bubble p {
    margin: 0;
    line-height: 1.4;
}

.message-bubble ul {
    margin: 10px 0 0 20px;
    padding: 0;
}

.message-bubble li {
    margin-bottom: 5px;
}

.message-time {
    font-size: 12px;
    color: var(--text-secondary);
    padding: 0 5px;
}

.quick-replies {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.quick-reply {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-reply:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.quick-reply i {
    font-size: 12px;
}

.chat-input-container {
    padding: 20px 25px;
    background: white;
    border-top: 1px solid var(--border-color);
}

.chat-input {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: var(--bg-light);
    border-radius: 25px;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.chat-input:focus-within {
    border-color: var(--primary-color);
}

.attachment-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.attachment-btn:hover {
    background: var(--primary-color);
    color: white;
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 16px;
    resize: none;
    outline: none;
    min-height: 20px;
    max-height: 120px;
    line-height: 1.4;
    padding: 8px 0;
}

.input-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.emoji-btn,
.send-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.emoji-btn {
    background: transparent;
    color: var(--text-secondary);
}

.emoji-btn:hover {
    background: var(--accent-color);
    color: white;
}

.send-btn {
    background: var(--primary-color);
    color: white;
}

.send-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 12px;
    color: var(--text-secondary);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-style: italic;
}

.chat-info-sidebar {
    position: fixed;
    top: 80px;
    right: -400px;
    width: 400px;
    height: calc(100vh - 80px);
    background: white;
    border-left: 1px solid var(--border-color);
    transition: right 0.3s ease;
    z-index: 100;
    overflow-y: auto;
}

.chat-info-sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-light);
}

.sidebar-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-sidebar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-sidebar:hover {
    background: var(--danger-color);
    color: white;
}

.sidebar-content {
    padding: 25px;
}

.info-section {
    margin-bottom: 30px;
}

.info-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.capability-list {
    list-style: none;
    padding: 0;
}

.capability-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    font-size: 14px;
    color: var(--text-secondary);
}

.capability-list i {
    color: var(--secondary-color);
    font-size: 12px;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn-full {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.action-btn-full:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chat-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.resource-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.resource-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--bg-light);
    border-radius: 8px;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.resource-link:hover {
    background: var(--primary-color);
    color: white;
}

.resource-link i {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.resource-link:hover i {
    color: white;
}

/* Notifications Page Styles */
.notifications-main {
    padding-top: 0;
}

.page-header .header-content {
    flex: 1;
}

.header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.notification-filters {
    background: white;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

.filter-tabs {
    display: flex;
    gap: 5px;
    background: var(--bg-light);
    padding: 5px;
    border-radius: 12px;
    overflow-x: auto;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.filter-tab.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow);
}

.filter-tab .count {
    background: var(--text-secondary);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.filter-tab.active .count {
    background: var(--primary-color);
}

.notifications-list {
    padding: 40px 0;
    background: var(--bg-light);
}

.notification-group {
    margin-bottom: 40px;
}

.group-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--border-color);
}

.notification-item {
    display: flex;
    gap: 15px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    border-left: 4px solid transparent;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.notification-item.unread {
    border-left-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.02), white);
}

.notification-item.urgent {
    border-left-color: var(--danger-color);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.02), white);
}

.notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.notification-icon.urgent {
    background: var(--danger-color);
    color: white;
}

.notification-icon.success {
    background: var(--secondary-color);
    color: white;
}

.notification-icon.warning {
    background: var(--warning-color);
    color: white;
}

.notification-icon.info {
    background: var(--primary-color);
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.notification-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
}

.notification-time {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    margin-left: 15px;
}

.notification-content p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 15px;
}

.notification-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-dark);
}

.action-btn.secondary {
    background: var(--bg-light);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.action-btn.secondary:hover {
    background: var(--border-color);
}

.notification-menu {
    position: relative;
}

.menu-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-btn:hover {
    background: var(--bg-light);
    color: var(--text-primary);
}

.menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    padding: 8px 0;
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 100;
}

.menu-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.menu-dropdown button {
    width: 100%;
    padding: 10px 16px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    text-align: left;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 14px;
}

.menu-dropdown button:hover {
    background: var(--bg-light);
}

.nav-icon.active {
    background: var(--primary-color);
    color: white;
}

/* Responsive Design for Notifications */
@media (max-width: 768px) {
    .page-header .container {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .filter-tabs {
        padding: 3px;
    }

    .filter-tab {
        padding: 10px 15px;
        font-size: 14px;
    }

    .notification-item {
        padding: 15px;
        gap: 12px;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .notification-header {
        flex-direction: column;
        gap: 5px;
        align-items: flex-start;
    }

    .notification-time {
        margin-left: 0;
    }

    .notification-actions {
        gap: 8px;
    }

    .action-btn {
        padding: 6px 12px;
        font-size: 13px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 40px;
        padding: 100px 20px 40px;
    }

    .hero-text h1 {
        font-size: 36px;
    }

    .hero-illustration {
        font-size: 120px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .hero-buttons {
        justify-content: center;
    }

    .btn {
        padding: 12px 24px;
    }

    .auth-form-container {
        padding: 30px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .social-login {
        grid-template-columns: 1fr;
    }

    .auth-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}
