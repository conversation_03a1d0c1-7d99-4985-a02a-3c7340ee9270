<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Test - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="test-attempt">
    <!-- Test Header -->
    <header class="test-header">
        <div class="test-nav">
            <div class="test-info">
                <h2>MDCAT Mock Test #5</h2>
                <p>Medical College Admission Test</p>
            </div>
            <div class="test-timer">
                <div class="timer-display">
                    <i class="fas fa-clock"></i>
                    <span id="timeRemaining">02:45:30</span>
                </div>
                <div class="timer-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="timerProgress"></div>
                    </div>
                </div>
            </div>
            <div class="test-actions">
                <button class="btn btn-outline" onclick="pauseTest()">
                    <i class="fas fa-pause"></i>
                    Pause
                </button>
                <button class="btn btn-danger" onclick="submitTest()">
                    <i class="fas fa-check"></i>
                    Submit
                </button>
            </div>
        </div>
    </header>

    <!-- Main Test Content -->
    <main class="test-main">
        <!-- Question Navigation Sidebar -->
        <aside class="question-nav-sidebar">
            <div class="nav-header">
                <h3>Questions</h3>
                <div class="nav-stats">
                    <span class="answered">15</span> / <span class="total">200</span>
                </div>
            </div>
            
            <div class="question-grid" id="questionGrid">
                <!-- Questions 1-20 will be generated by JavaScript -->
            </div>
            
            <div class="nav-legend">
                <div class="legend-item">
                    <span class="legend-color answered"></span>
                    <span>Answered</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color flagged"></span>
                    <span>Flagged</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color current"></span>
                    <span>Current</span>
                </div>
                <div class="legend-item">
                    <span class="legend-color unanswered"></span>
                    <span>Unanswered</span>
                </div>
            </div>
        </aside>

        <!-- Question Content -->
        <section class="question-content">
            <div class="question-header">
                <div class="question-info">
                    <span class="question-number">Question 16 of 200</span>
                    <span class="subject-tag">Biology</span>
                </div>
                <div class="question-actions">
                    <button class="action-btn" onclick="flagQuestion()" id="flagBtn">
                        <i class="far fa-flag"></i>
                        Flag
                    </button>
                    <button class="action-btn" onclick="clearAnswer()">
                        <i class="fas fa-eraser"></i>
                        Clear
                    </button>
                </div>
            </div>

            <div class="question-body">
                <div class="question-text">
                    <p><strong>Which of the following is the powerhouse of the cell?</strong></p>
                    <div class="question-image" style="display: none;">
                        <!-- Question image would go here if needed -->
                    </div>
                </div>

                <div class="answer-options">
                    <div class="option" data-option="A">
                        <input type="radio" name="answer" id="optionA" value="A">
                        <label for="optionA">
                            <span class="option-letter">A</span>
                            <span class="option-text">Nucleus</span>
                        </label>
                    </div>
                    <div class="option" data-option="B">
                        <input type="radio" name="answer" id="optionB" value="B">
                        <label for="optionB">
                            <span class="option-letter">B</span>
                            <span class="option-text">Mitochondria</span>
                        </label>
                    </div>
                    <div class="option" data-option="C">
                        <input type="radio" name="answer" id="optionC" value="C">
                        <label for="optionC">
                            <span class="option-letter">C</span>
                            <span class="option-text">Ribosome</span>
                        </label>
                    </div>
                    <div class="option" data-option="D">
                        <input type="radio" name="answer" id="optionD" value="D">
                        <label for="optionD">
                            <span class="option-letter">D</span>
                            <span class="option-text">Endoplasmic Reticulum</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="question-footer">
                <div class="navigation-buttons">
                    <button class="btn btn-outline" onclick="previousQuestion()" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                        Previous
                    </button>
                    <button class="btn btn-primary" onclick="nextQuestion()" id="nextBtn">
                        Next
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                
                <div class="question-progress">
                    <div class="progress-info">
                        <span>Progress: 8%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 8%"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Test Pause Modal -->
    <div class="modal" id="pauseModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Test Paused</h3>
                <button class="close-modal" onclick="closeModal('pauseModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="pause-info">
                    <i class="fas fa-pause-circle"></i>
                    <p>Your test has been paused. You can resume anytime.</p>
                    <div class="pause-stats">
                        <div class="stat">
                            <span class="label">Time Remaining:</span>
                            <span class="value" id="pausedTime">02:45:30</span>
                        </div>
                        <div class="stat">
                            <span class="label">Questions Answered:</span>
                            <span class="value">15 / 200</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="exitTest()">Exit Test</button>
                <button class="btn btn-primary" onclick="resumeTest()">Resume Test</button>
            </div>
        </div>
    </div>

    <!-- Submit Test Modal -->
    <div class="modal" id="submitModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Submit Test</h3>
                <button class="close-modal" onclick="closeModal('submitModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="submit-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Are you sure you want to submit your test?</p>
                    <div class="submit-stats">
                        <div class="stat-row">
                            <span>Total Questions:</span>
                            <span>200</span>
                        </div>
                        <div class="stat-row">
                            <span>Answered:</span>
                            <span class="answered-count">15</span>
                        </div>
                        <div class="stat-row">
                            <span>Unanswered:</span>
                            <span class="unanswered-count">185</span>
                        </div>
                        <div class="stat-row">
                            <span>Flagged:</span>
                            <span class="flagged-count">3</span>
                        </div>
                    </div>
                    <p class="warning-text">Once submitted, you cannot make any changes to your answers.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal('submitModal')">Continue Test</button>
                <button class="btn btn-danger" onclick="confirmSubmit()">Submit Test</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // Test attempt functionality
        let currentQuestion = 16;
        let totalQuestions = 200;
        let timeRemaining = 9930; // seconds (2:45:30)
        let testTimer;
        let isPaused = false;
        let answers = {};
        let flaggedQuestions = new Set([5, 12, 23]);

        // Sample questions data
        const questions = {
            16: {
                subject: 'Biology',
                text: 'Which of the following is the powerhouse of the cell?',
                options: {
                    A: 'Nucleus',
                    B: 'Mitochondria',
                    C: 'Ribosome',
                    D: 'Endoplasmic Reticulum'
                }
            }
            // More questions would be loaded here
        };

        function initializeTest() {
            generateQuestionGrid();
            startTimer();
            updateQuestionDisplay();
            
            // Auto-save answers
            document.querySelectorAll('input[name="answer"]').forEach(input => {
                input.addEventListener('change', saveAnswer);
            });
        }

        function generateQuestionGrid() {
            const grid = document.getElementById('questionGrid');
            grid.innerHTML = '';
            
            for (let i = 1; i <= Math.min(totalQuestions, 50); i++) {
                const questionBtn = document.createElement('button');
                questionBtn.className = 'question-btn';
                questionBtn.textContent = i;
                questionBtn.onclick = () => goToQuestion(i);
                
                // Set question status
                if (i === currentQuestion) {
                    questionBtn.classList.add('current');
                } else if (answers[i]) {
                    questionBtn.classList.add('answered');
                } else if (flaggedQuestions.has(i)) {
                    questionBtn.classList.add('flagged');
                } else {
                    questionBtn.classList.add('unanswered');
                }
                
                grid.appendChild(questionBtn);
            }
        }

        function startTimer() {
            testTimer = setInterval(() => {
                if (!isPaused) {
                    timeRemaining--;
                    updateTimerDisplay();
                    
                    if (timeRemaining <= 0) {
                        clearInterval(testTimer);
                        autoSubmitTest();
                    }
                }
            }, 1000);
        }

        function updateTimerDisplay() {
            const hours = Math.floor(timeRemaining / 3600);
            const minutes = Math.floor((timeRemaining % 3600) / 60);
            const seconds = timeRemaining % 60;
            
            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timeRemaining').textContent = timeString;
            
            // Update progress bar (assuming 3 hours total)
            const totalTime = 10800; // 3 hours in seconds
            const progress = ((totalTime - timeRemaining) / totalTime) * 100;
            document.getElementById('timerProgress').style.width = progress + '%';
        }

        function updateQuestionDisplay() {
            document.querySelector('.question-number').textContent = `Question ${currentQuestion} of ${totalQuestions}`;
            
            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentQuestion === 1;
            document.getElementById('nextBtn').textContent = currentQuestion === totalQuestions ? 'Finish' : 'Next';
            
            // Load saved answer if exists
            if (answers[currentQuestion]) {
                document.querySelector(`input[value="${answers[currentQuestion]}"]`).checked = true;
            } else {
                document.querySelectorAll('input[name="answer"]').forEach(input => {
                    input.checked = false;
                });
            }
            
            // Update flag button
            const flagBtn = document.getElementById('flagBtn');
            if (flaggedQuestions.has(currentQuestion)) {
                flagBtn.innerHTML = '<i class="fas fa-flag"></i> Unflag';
                flagBtn.classList.add('flagged');
            } else {
                flagBtn.innerHTML = '<i class="far fa-flag"></i> Flag';
                flagBtn.classList.remove('flagged');
            }
            
            generateQuestionGrid();
        }

        function saveAnswer() {
            const selectedAnswer = document.querySelector('input[name="answer"]:checked');
            if (selectedAnswer) {
                answers[currentQuestion] = selectedAnswer.value;
                updateStats();
            }
        }

        function updateStats() {
            const answeredCount = Object.keys(answers).length;
            document.querySelector('.answered').textContent = answeredCount;
            document.querySelector('.answered-count').textContent = answeredCount;
            document.querySelector('.unanswered-count').textContent = totalQuestions - answeredCount;
            document.querySelector('.flagged-count').textContent = flaggedQuestions.size;
            
            // Update progress
            const progress = (answeredCount / totalQuestions) * 100;
            document.querySelector('.question-progress .progress-fill').style.width = progress + '%';
            document.querySelector('.question-progress .progress-info span').textContent = `Progress: ${Math.round(progress)}%`;
        }

        function nextQuestion() {
            saveAnswer();
            if (currentQuestion < totalQuestions) {
                currentQuestion++;
                updateQuestionDisplay();
            } else {
                submitTest();
            }
        }

        function previousQuestion() {
            saveAnswer();
            if (currentQuestion > 1) {
                currentQuestion--;
                updateQuestionDisplay();
            }
        }

        function goToQuestion(questionNum) {
            saveAnswer();
            currentQuestion = questionNum;
            updateQuestionDisplay();
        }

        function flagQuestion() {
            if (flaggedQuestions.has(currentQuestion)) {
                flaggedQuestions.delete(currentQuestion);
            } else {
                flaggedQuestions.add(currentQuestion);
            }
            updateQuestionDisplay();
            updateStats();
        }

        function clearAnswer() {
            delete answers[currentQuestion];
            document.querySelectorAll('input[name="answer"]').forEach(input => {
                input.checked = false;
            });
            updateQuestionDisplay();
            updateStats();
        }

        function pauseTest() {
            isPaused = true;
            document.getElementById('pausedTime').textContent = document.getElementById('timeRemaining').textContent;
            document.getElementById('pauseModal').classList.add('active');
        }

        function resumeTest() {
            isPaused = false;
            closeModal('pauseModal');
        }

        function submitTest() {
            document.getElementById('submitModal').classList.add('active');
        }

        function confirmSubmit() {
            clearInterval(testTimer);
            showNotification('Test submitted successfully! Redirecting to results...', 'success');
            setTimeout(() => {
                window.location.href = 'mock-test.html';
            }, 2000);
        }

        function autoSubmitTest() {
            showNotification('Time is up! Test submitted automatically.', 'warning');
            setTimeout(() => {
                window.location.href = 'mock-test.html';
            }, 2000);
        }

        function exitTest() {
            if (confirm('Are you sure you want to exit the test? Your progress will be lost.')) {
                clearInterval(testTimer);
                window.location.href = 'mock-test.html';
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Prevent accidental page refresh
        window.addEventListener('beforeunload', function(e) {
            if (!isPaused) {
                e.preventDefault();
                e.returnValue = 'Are you sure you want to leave? Your test progress may be lost.';
            }
        });

        // Initialize test when page loads
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
