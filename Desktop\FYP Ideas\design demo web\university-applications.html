<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Applications Management - University Portal</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="university-applications">
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <button class="theme-option light active" onclick="setTheme('light')" title="Light Mode">
            <i class="fas fa-sun"></i>
        </button>
        <button class="theme-option dark" onclick="setTheme('dark')" title="Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <div class="university-layout">
        <!-- University Sidebar -->
        <aside class="university-sidebar">
            <div class="sidebar-header">
                <div class="university-logo">
                    <i class="fas fa-university"></i>
                    <span>University of Punjab</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="university-dashboard.html" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="university-applications.html" class="nav-link">
                            <i class="fas fa-file-alt"></i>
                            <span>Applications</span>
                            <span class="nav-badge">156</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="university-programs.html" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Programs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="university-cms.html" class="nav-link">
                            <i class="fas fa-edit"></i>
                            <span>Content Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="university-settings.html" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="university-profile">
                    <img src="https://via.placeholder.com/40x40/059669/ffffff?text=PU" alt="University" class="university-avatar">
                    <div class="university-info">
                        <span class="university-name">University of Punjab</span>
                        <span class="university-role">Admin Portal</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="universityLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="university-main">
            <!-- Header -->
            <header class="university-header">
                <div class="header-left">
                    <button class="mobile-menu-btn" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="header-text">
                        <h1>Applications Management</h1>
                        <p>Review and process student applications</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn" onclick="exportApplications()">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                        <button class="action-btn primary" onclick="bulkAction()">
                            <i class="fas fa-tasks"></i>
                            Bulk Actions
                        </button>
                    </div>
                </div>
            </header>

            <!-- Applications Content -->
            <div class="applications-container">
                <!-- Filters -->
                <div class="applications-filters">
                    <div class="filter-group">
                        <label>Status</label>
                        <select id="statusFilter" onchange="filterApplications()">
                            <option value="">All Status</option>
                            <option value="pending">Pending Review</option>
                            <option value="under-review">Under Review</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Program</label>
                        <select id="programFilter" onchange="filterApplications()">
                            <option value="">All Programs</option>
                            <option value="cs">Computer Science</option>
                            <option value="mba">Business Administration</option>
                            <option value="eng">Engineering</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Date Range</label>
                        <select id="dateFilter" onchange="filterApplications()">
                            <option value="">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn btn-outline" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>

                <!-- Applications Table -->
                <div class="applications-table-container">
                    <table class="applications-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>Student</th>
                                <th>Program</th>
                                <th>Applied Date</th>
                                <th>Status</th>
                                <th>Documents</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="applicationsTableBody">
                            <tr class="application-row" data-status="pending" data-program="cs">
                                <td>
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td>
                                    <div class="student-info">
                                        <img src="https://via.placeholder.com/40x40/1e40af/ffffff?text=AK" alt="Student" class="student-avatar">
                                        <div class="student-details">
                                            <h4>Ahmad Khan</h4>
                                            <p><EMAIL></p>
                                            <span class="student-id">ID: STU001</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="program-info">
                                        <h4>Computer Science</h4>
                                        <p>BS - 4 Years</p>
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <span class="date">Aug 10, 2024</span>
                                        <span class="time">2:30 PM</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge pending">Pending Review</span>
                                </td>
                                <td>
                                    <div class="documents-status">
                                        <span class="doc-count">8/10</span>
                                        <span class="doc-status incomplete">Incomplete</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" onclick="viewApplication('STU001')" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" onclick="approveApplication('STU001')" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="action-btn danger" onclick="rejectApplication('STU001')" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <tr class="application-row" data-status="approved" data-program="mba">
                                <td>
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td>
                                    <div class="student-info">
                                        <img src="https://via.placeholder.com/40x40/059669/ffffff?text=SA" alt="Student" class="student-avatar">
                                        <div class="student-details">
                                            <h4>Sarah Ahmed</h4>
                                            <p><EMAIL></p>
                                            <span class="student-id">ID: STU002</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="program-info">
                                        <h4>Business Administration</h4>
                                        <p>MBA - 2 Years</p>
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <span class="date">Aug 9, 2024</span>
                                        <span class="time">11:15 AM</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge approved">Approved</span>
                                </td>
                                <td>
                                    <div class="documents-status">
                                        <span class="doc-count">10/10</span>
                                        <span class="doc-status complete">Complete</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" onclick="viewApplication('STU002')" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" onclick="generateChallan('STU002')" title="Generate Challan">
                                            <i class="fas fa-receipt"></i>
                                        </button>
                                        <button class="action-btn" onclick="sendNotification('STU002')" title="Send Notification">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <tr class="application-row" data-status="under-review" data-program="eng">
                                <td>
                                    <input type="checkbox" class="row-checkbox">
                                </td>
                                <td>
                                    <div class="student-info">
                                        <img src="https://via.placeholder.com/40x40/dc2626/ffffff?text=MH" alt="Student" class="student-avatar">
                                        <div class="student-details">
                                            <h4>Muhammad Hassan</h4>
                                            <p><EMAIL></p>
                                            <span class="student-id">ID: STU003</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="program-info">
                                        <h4>Engineering</h4>
                                        <p>BE - 4 Years</p>
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <span class="date">Aug 8, 2024</span>
                                        <span class="time">4:45 PM</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge under-review">Under Review</span>
                                </td>
                                <td>
                                    <div class="documents-status">
                                        <span class="doc-count">9/10</span>
                                        <span class="doc-status incomplete">Incomplete</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" onclick="viewApplication('STU003')" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn" onclick="requestDocuments('STU003')" title="Request Documents">
                                            <i class="fas fa-file-upload"></i>
                                        </button>
                                        <button class="action-btn danger" onclick="rejectApplication('STU003')" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        Showing 1-3 of 156 applications
                    </div>
                    <div class="pagination">
                        <button class="page-btn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <span class="page-dots">...</span>
                        <button class="page-btn">52</button>
                        <button class="page-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Notification function
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                padding: 16px 20px;
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 12px;
                max-width: 400px;
                border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
            const color = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';

            notification.innerHTML = `
                <i class="fas fa-${icon}" style="color: ${color}; font-size: 18px;"></i>
                <span style="color: #374151; font-weight: 500;">${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px; margin-left: auto;">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => notification.remove(), 5000);
        }
    </script>
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.university-sidebar');
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('mobile-open');
            } else {
                sidebar.classList.toggle('collapsed');
            }
        }

        function universityLogout() {
            if (confirm('Are you sure you want to logout?')) {
                showNotification('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'university-login.html';
                }, 1500);
            }
        }

        function filterApplications() {
            showNotification('Filtering applications...', 'info');
        }

        function clearFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('programFilter').value = '';
            document.getElementById('dateFilter').value = '';
            showNotification('Filters cleared', 'info');
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
        }

        function viewApplication(studentId) {
            showNotification(`Opening application details for ${studentId}...`, 'info');
        }

        function approveApplication(studentId) {
            if (confirm(`Approve application for ${studentId}?`)) {
                showNotification(`Application ${studentId} approved successfully!`, 'success');
            }
        }

        function rejectApplication(studentId) {
            if (confirm(`Reject application for ${studentId}?`)) {
                showNotification(`Application ${studentId} rejected.`, 'warning');
            }
        }

        function generateChallan(studentId) {
            showNotification(`Generating challan for ${studentId}...`, 'info');
        }

        function sendNotification(studentId) {
            showNotification(`Sending notification to ${studentId}...`, 'info');
        }

        function requestDocuments(studentId) {
            showNotification(`Requesting missing documents from ${studentId}...`, 'info');
        }

        function exportApplications() {
            showNotification('Exporting applications data...', 'info');
        }

        function bulkAction() {
            showNotification('Opening bulk actions menu...', 'info');
        }
    </script>
</body>
</html>
