<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniConnect Pakistan - Test Page</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="test">
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <button class="theme-option light active" onclick="setTheme('light')" title="Light Mode">
            <i class="fas fa-sun"></i>
        </button>
        <button class="theme-option dark" onclick="setTheme('dark')" title="Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <div class="container" style="padding: 40px 20px;">
        <h1 style="text-align: center; margin-bottom: 40px; color: var(--text-primary);">
            🎓 UniConnect Pakistan - Test Page
        </h1>

        <!-- Navigation Links -->
        <div style="background: var(--bg-card); padding: 30px; border-radius: 16px; margin-bottom: 30px; box-shadow: var(--shadow);">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">🔗 Navigation Test</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="index.html" class="btn btn-primary">🏠 Home Page</a>
                <a href="login.html" class="btn btn-secondary">👨‍🎓 Student Login</a>
                <a href="university-login.html" class="btn btn-outline">🏛️ University Login</a>
                <a href="admin-login.html" class="btn btn-danger">🛡️ Admin Login</a>
                <a href="universities.html" class="btn btn-ghost">📚 Universities</a>
                <a href="dashboard.html" class="btn btn-primary">📊 Student Dashboard</a>
                <a href="university-dashboard.html" class="btn btn-secondary">🏛️ University Dashboard</a>
                <a href="university-cms.html" class="btn btn-outline">📝 University CMS</a>
            </div>
        </div>

        <!-- Button Test -->
        <div style="background: var(--bg-card); padding: 30px; border-radius: 16px; margin-bottom: 30px; box-shadow: var(--shadow);">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">🔘 Button Test</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px;">
                <button class="btn btn-primary" onclick="testNotification('success')">Primary Button</button>
                <button class="btn btn-secondary" onclick="testNotification('info')">Secondary Button</button>
                <button class="btn btn-outline" onclick="testNotification('warning')">Outline Button</button>
                <button class="btn btn-danger" onclick="testNotification('error')">Danger Button</button>
                <button class="btn btn-ghost">Ghost Button</button>
            </div>
            <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                <button class="btn btn-primary btn-sm">Small</button>
                <button class="btn btn-secondary">Medium</button>
                <button class="btn btn-outline btn-lg">Large</button>
                <button class="btn btn-primary" disabled>Disabled</button>
            </div>
        </div>

        <!-- Form Test -->
        <div style="background: var(--bg-card); padding: 30px; border-radius: 16px; margin-bottom: 30px; box-shadow: var(--shadow);">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">📝 Form Test</h2>
            <form onsubmit="testForm(event)">
                <div class="form-group">
                    <label for="testEmail">Email</label>
                    <div class="input-group">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="testEmail" class="form-control" placeholder="Enter your email" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="testPassword">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="testPassword" class="form-control" placeholder="Enter password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('testPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="testSelect">University</label>
                    <select id="testSelect" class="form-control" required>
                        <option value="">Select University</option>
                        <option value="pu">University of Punjab</option>
                        <option value="ku">University of Karachi</option>
                        <option value="lums">LUMS</option>
                        <option value="nust">NUST</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Submit Test Form</button>
            </form>
        </div>

        <!-- Badge Test -->
        <div style="background: var(--bg-card); padding: 30px; border-radius: 16px; margin-bottom: 30px; box-shadow: var(--shadow);">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">🏷️ Badge Test</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <span class="badge badge-primary">Primary</span>
                <span class="badge badge-secondary">Secondary</span>
                <span class="badge badge-success">Success</span>
                <span class="badge badge-warning">Warning</span>
                <span class="badge badge-danger">Danger</span>
                <span class="badge badge-info">Info</span>
            </div>
        </div>

        <!-- Card Test -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div class="card">
                <div class="card-header">
                    <h3>Default Card</h3>
                </div>
                <div class="card-body">
                    <p>This is a default card with header and body.</p>
                </div>
                <div class="card-footer">
                    <button class="btn btn-primary btn-sm">Action</button>
                </div>
            </div>
            
            <div class="card" style="border: none; box-shadow: var(--shadow-lg);">
                <div class="card-body">
                    <h3>Elevated Card</h3>
                    <p>This card has enhanced shadow for elevation effect.</p>
                    <button class="btn btn-outline btn-sm">Learn More</button>
                </div>
            </div>
        </div>

        <!-- Loading Test -->
        <div style="background: var(--bg-card); padding: 30px; border-radius: 16px; margin-bottom: 30px; box-shadow: var(--shadow);">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">⏳ Loading Test</h2>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button class="btn btn-primary" onclick="testLoading(this)">Test Loading</button>
                <div class="loading-spinner"></div>
                <div class="loading-skeleton" style="width: 200px; height: 20px;"></div>
            </div>
        </div>

        <!-- Demo Credentials -->
        <div style="background: var(--bg-secondary); padding: 30px; border-radius: 16px; border-left: 4px solid var(--primary-color);">
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">🔑 Demo Credentials</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">Student Portal</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> student123</p>
                </div>
                <div>
                    <h4 style="color: var(--secondary-color); margin-bottom: 10px;">University Portal</h4>
                    <p><strong>Code:</strong> PU2024</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> university123</p>
                </div>
                <div>
                    <h4 style="color: var(--danger-color); margin-bottom: 10px;">Admin Portal</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> admin123</p>
                    <p><strong>Role:</strong> Super Admin</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        function testNotification(type) {
            const messages = {
                'success': 'Success! Everything is working perfectly.',
                'error': 'Error! Something went wrong.',
                'warning': 'Warning! Please check your input.',
                'info': 'Info! This is an information message.'
            };
            showNotification(messages[type], type);
        }

        function testForm(event) {
            event.preventDefault();
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            const university = document.getElementById('testSelect').value;
            
            if (email && password && university) {
                showNotification(`Form submitted successfully! Email: ${email}, University: ${university}`, 'success');
            } else {
                showNotification('Please fill in all required fields.', 'error');
            }
        }

        function testLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showNotification('Loading test completed!', 'success');
            }, 3000);
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const toggle = input.parentElement.querySelector('.password-toggle i');
            
            if (input.type === 'password') {
                input.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }

        // Theme toggle function
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            document.querySelectorAll('.theme-option').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.theme-option.${theme}`).classList.add('active');
        }

        // Notification function
        function showNotification(message, type = 'info') {
            const existing = document.querySelectorAll('.notification');
            existing.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                padding: 16px 20px;
                z-index: 9999;
                display: flex;
                align-items: center;
                gap: 12px;
                max-width: 400px;
                border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            
            const colors = {
                'success': '#10b981',
                'error': '#ef4444',
                'warning': '#f59e0b',
                'info': '#3b82f6'
            };
            
            notification.innerHTML = `
                <i class="fas fa-${icons[type]}" style="color: ${colors[type]}; font-size: 18px;"></i>
                <span style="color: #374151; font-weight: 500;">${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px; margin-left: auto;">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);
            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => notification.remove(), 5000);
        }

        // Initialize theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            setTheme(savedTheme);
        });
    </script>
</body>
</html>
