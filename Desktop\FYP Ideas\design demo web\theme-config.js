// UniConnect Pakistan - Unified Theme Configuration
const THEME_CONFIG = {
    // Brand Identity
    brand: {
        name: 'UniConnect Pakistan',
        tagline: 'Your Gateway to Pakistani Universities',
        logo: {
            primary: 'fas fa-graduation-cap',
            university: 'fas fa-university',
            admin: 'fas fa-shield-alt'
        }
    },

    // Color Palette
    colors: {
        // Primary Brand Colors
        primary: {
            50: '#eff6ff',
            100: '#dbeafe',
            200: '#bfdbfe',
            300: '#93c5fd',
            400: '#60a5fa',
            500: '#3b82f6',  // Main primary
            600: '#2563eb',
            700: '#1d4ed8',
            800: '#1e40af',  // Primary dark
            900: '#1e3a8a'
        },
        
        // Secondary Brand Colors
        secondary: {
            50: '#ecfdf5',
            100: '#d1fae5',
            200: '#a7f3d0',
            300: '#6ee7b7',
            400: '#34d399',
            500: '#10b981',  // Main secondary
            600: '#059669',  // Secondary dark
            700: '#047857',
            800: '#065f46',
            900: '#064e3b'
        },

        // Accent Colors
        accent: {
            gold: '#f59e0b',
            orange: '#ea580c',
            purple: '#7c3aed'
        },

        // Status Colors
        status: {
            success: '#10b981',
            warning: '#f59e0b',
            danger: '#ef4444',
            info: '#3b82f6'
        },

        // Neutral Colors
        gray: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a'
        }
    },

    // Typography
    typography: {
        fontFamily: {
            primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            heading: "'Inter', sans-serif",
            mono: "'JetBrains Mono', 'Fira Code', monospace"
        },
        fontSize: {
            xs: '0.75rem',    // 12px
            sm: '0.875rem',   // 14px
            base: '1rem',     // 16px
            lg: '1.125rem',   // 18px
            xl: '1.25rem',    // 20px
            '2xl': '1.5rem',  // 24px
            '3xl': '1.875rem', // 30px
            '4xl': '2.25rem', // 36px
            '5xl': '3rem'     // 48px
        },
        fontWeight: {
            light: 300,
            normal: 400,
            medium: 500,
            semibold: 600,
            bold: 700,
            extrabold: 800
        },
        lineHeight: {
            tight: 1.25,
            snug: 1.375,
            normal: 1.5,
            relaxed: 1.625,
            loose: 2
        }
    },

    // Spacing Scale
    spacing: {
        0: '0',
        1: '0.25rem',   // 4px
        2: '0.5rem',    // 8px
        3: '0.75rem',   // 12px
        4: '1rem',      // 16px
        5: '1.25rem',   // 20px
        6: '1.5rem',    // 24px
        8: '2rem',      // 32px
        10: '2.5rem',   // 40px
        12: '3rem',     // 48px
        16: '4rem',     // 64px
        20: '5rem',     // 80px
        24: '6rem',     // 96px
        32: '8rem'      // 128px
    },

    // Border Radius
    borderRadius: {
        none: '0',
        sm: '0.25rem',    // 4px
        md: '0.375rem',   // 6px
        lg: '0.5rem',     // 8px
        xl: '0.75rem',    // 12px
        '2xl': '1rem',    // 16px
        '3xl': '1.5rem',  // 24px
        full: '9999px'
    },

    // Shadows
    shadows: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        default: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
    },

    // Transitions
    transitions: {
        fast: '150ms cubic-bezier(0.4, 0, 0.2, 1)',
        normal: '300ms cubic-bezier(0.4, 0, 0.2, 1)',
        slow: '500ms cubic-bezier(0.4, 0, 0.2, 1)'
    },

    // Z-Index Scale
    zIndex: {
        dropdown: 1000,
        sticky: 1020,
        fixed: 1030,
        modalBackdrop: 1040,
        modal: 1050,
        popover: 1060,
        tooltip: 1070,
        toast: 1080
    },

    // Component Variants
    components: {
        button: {
            sizes: {
                xs: { padding: '6px 12px', fontSize: '12px' },
                sm: { padding: '8px 16px', fontSize: '13px' },
                md: { padding: '14px 28px', fontSize: '14px' },
                lg: { padding: '16px 32px', fontSize: '16px' },
                xl: { padding: '20px 40px', fontSize: '18px' }
            },
            variants: {
                primary: { bg: 'primary.600', color: 'white', hover: 'primary.700' },
                secondary: { bg: 'secondary.600', color: 'white', hover: 'secondary.700' },
                outline: { bg: 'transparent', color: 'primary.600', border: 'primary.600' },
                ghost: { bg: 'transparent', color: 'gray.600', hover: 'gray.100' },
                danger: { bg: 'red.600', color: 'white', hover: 'red.700' }
            }
        },
        
        card: {
            variants: {
                default: { bg: 'white', shadow: 'default', border: 'gray.200' },
                elevated: { bg: 'white', shadow: 'lg', border: 'none' },
                outlined: { bg: 'white', shadow: 'none', border: 'gray.200' }
            }
        },

        badge: {
            variants: {
                primary: { bg: 'primary.100', color: 'primary.800' },
                secondary: { bg: 'secondary.100', color: 'secondary.800' },
                success: { bg: 'green.100', color: 'green.800' },
                warning: { bg: 'yellow.100', color: 'yellow.800' },
                danger: { bg: 'red.100', color: 'red.800' },
                info: { bg: 'blue.100', color: 'blue.800' }
            }
        }
    },

    // Breakpoints
    breakpoints: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px'
    },

    // Animation Presets
    animations: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
        slideUp: 'slideUp 0.3s ease-out',
        slideDown: 'slideDown 0.3s ease-out',
        scaleIn: 'scaleIn 0.2s ease-out',
        spin: 'spin 1s linear infinite'
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = THEME_CONFIG;
}

// Global theme utilities
window.THEME_CONFIG = THEME_CONFIG;

// Theme application function
function applyTheme(theme = 'light') {
    const root = document.documentElement;
    const config = THEME_CONFIG;
    
    // Apply color variables
    if (theme === 'light') {
        root.style.setProperty('--primary-color', config.colors.primary[600]);
        root.style.setProperty('--primary-dark', config.colors.primary[800]);
        root.style.setProperty('--primary-light', config.colors.primary[500]);
        root.style.setProperty('--secondary-color', config.colors.secondary[600]);
        root.style.setProperty('--text-primary', config.colors.gray[900]);
        root.style.setProperty('--text-secondary', config.colors.gray[600]);
        root.style.setProperty('--text-muted', config.colors.gray[500]);
        root.style.setProperty('--bg-primary', '#ffffff');
        root.style.setProperty('--bg-secondary', config.colors.gray[50]);
        root.style.setProperty('--bg-tertiary', config.colors.gray[100]);
        root.style.setProperty('--border-color', config.colors.gray[200]);
    } else {
        // Dark theme variables
        root.style.setProperty('--primary-color', config.colors.primary[500]);
        root.style.setProperty('--primary-dark', config.colors.primary[600]);
        root.style.setProperty('--primary-light', config.colors.primary[400]);
        root.style.setProperty('--secondary-color', config.colors.secondary[500]);
        root.style.setProperty('--text-primary', config.colors.gray[100]);
        root.style.setProperty('--text-secondary', config.colors.gray[300]);
        root.style.setProperty('--text-muted', config.colors.gray[400]);
        root.style.setProperty('--bg-primary', config.colors.gray[900]);
        root.style.setProperty('--bg-secondary', config.colors.gray[800]);
        root.style.setProperty('--bg-tertiary', config.colors.gray[700]);
        root.style.setProperty('--border-color', config.colors.gray[600]);
    }
    
    // Store theme preference
    localStorage.setItem('theme', theme);
}

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    applyTheme(savedTheme);
    
    // Update theme toggle buttons
    const themeButtons = document.querySelectorAll('.theme-option');
    themeButtons.forEach(btn => {
        btn.classList.toggle('active', btn.classList.contains(savedTheme));
    });
});
