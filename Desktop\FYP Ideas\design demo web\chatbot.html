<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant - UniConnect Pakistan</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-page="chatbot">
    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-container">
            <div class="logo">
                <a href="dashboard.html">
                    <i class="fas fa-university"></i>
                    <span>UniConnect Pakistan</span>
                </a>
            </div>
            <div class="nav-center">
                <div class="chatbot-title">
                    <i class="fas fa-robot"></i>
                    <span>AI Assistant</span>
                </div>
            </div>
            <div class="nav-right">
                <div class="nav-icons">
                    <a href="notifications.html" class="nav-icon" data-tooltip="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </a>
                    <button class="nav-icon" onclick="clearChat()" data-tooltip="Clear Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                    <div class="profile-dropdown">
                        <button class="profile-btn">
                            <img src="https://via.placeholder.com/40x40/2563eb/ffffff?text=U" alt="Profile" class="profile-avatar">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="documents.html"><i class="fas fa-file-alt"></i> Documents</a>
                            <a href="#"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="index.html"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="chatbot-main">
        <div class="chat-container">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="bot-info">
                    <div class="bot-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="bot-details">
                        <h3>UniConnect AI Assistant</h3>
                        <p class="bot-status">
                            <span class="status-indicator online"></span>
                            Online - Ready to help
                        </p>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-btn" onclick="toggleChatInfo()" data-tooltip="Chat Info">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-messages" id="chatMessages">
                <!-- Welcome Message -->
                <div class="message bot-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            <p>Hello! I'm your AI assistant for university admissions in Pakistan. I can help you with:</p>
                            <ul>
                                <li>University information and requirements</li>
                                <li>Admission deadlines and procedures</li>
                                <li>Entry test preparation</li>
                                <li>Document requirements</li>
                                <li>Fee structures and scholarships</li>
                            </ul>
                            <p>How can I assist you today?</p>
                        </div>
                        <div class="message-time">Just now</div>
                    </div>
                </div>

                <!-- Quick Reply Suggestions -->
                <div class="quick-replies">
                    <button class="quick-reply" onclick="sendQuickReply('What are the admission requirements for LUMS?')">
                        <i class="fas fa-graduation-cap"></i>
                        LUMS Requirements
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('When is the MDCAT exam date?')">
                        <i class="fas fa-calendar"></i>
                        MDCAT Date
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('What documents do I need for university admission?')">
                        <i class="fas fa-file-alt"></i>
                        Required Documents
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('Tell me about engineering universities in Lahore')">
                        <i class="fas fa-cogs"></i>
                        Engineering Unis
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('What are the fee structures for medical colleges?')">
                        <i class="fas fa-money-bill-wave"></i>
                        Medical Fees
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('How to prepare for entry tests?')">
                        <i class="fas fa-brain"></i>
                        Test Preparation
                    </button>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="chat-input-container">
                <div class="chat-input">
                    <button class="attachment-btn" data-tooltip="Attach File">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <input type="text" id="messageInput" placeholder="Type your question here..." maxlength="500">
                    <div class="input-actions">
                        <button class="emoji-btn" data-tooltip="Emoji">
                            <i class="fas fa-smile"></i>
                        </button>
                        <button class="send-btn" onclick="sendMessage()" data-tooltip="Send Message">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
                <div class="input-footer">
                    <span class="character-count">0/500</span>
                    <span class="typing-indicator" id="typingIndicator" style="display: none;">
                        <i class="fas fa-robot"></i> AI is typing...
                    </span>
                </div>
            </div>
        </div>

        <!-- Chat Info Sidebar -->
        <div class="chat-info-sidebar" id="chatInfoSidebar">
            <div class="sidebar-header">
                <h3>Chat Information</h3>
                <button class="close-sidebar" onclick="toggleChatInfo()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="sidebar-content">
                <div class="info-section">
                    <h4>AI Capabilities</h4>
                    <ul class="capability-list">
                        <li><i class="fas fa-check"></i> University information</li>
                        <li><i class="fas fa-check"></i> Admission requirements</li>
                        <li><i class="fas fa-check"></i> Deadline reminders</li>
                        <li><i class="fas fa-check"></i> Test preparation tips</li>
                        <li><i class="fas fa-check"></i> Document guidance</li>
                        <li><i class="fas fa-check"></i> Fee information</li>
                    </ul>
                </div>
                
                <div class="info-section">
                    <h4>Quick Actions</h4>
                    <div class="quick-actions">
                        <button class="action-btn-full" onclick="exportChat()">
                            <i class="fas fa-download"></i>
                            Export Chat
                        </button>
                        <button class="action-btn-full" onclick="shareChat()">
                            <i class="fas fa-share"></i>
                            Share Chat
                        </button>
                        <button class="action-btn-full" onclick="reportIssue()">
                            <i class="fas fa-flag"></i>
                            Report Issue
                        </button>
                    </div>
                </div>

                <div class="info-section">
                    <h4>Chat Statistics</h4>
                    <div class="chat-stats">
                        <div class="stat-item">
                            <span class="stat-label">Messages Today</span>
                            <span class="stat-value">12</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Conversations</span>
                            <span class="stat-value">45</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Avg Response Time</span>
                            <span class="stat-value">2.3s</span>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <h4>Helpful Resources</h4>
                    <div class="resource-links">
                        <a href="universities.html" class="resource-link">
                            <i class="fas fa-university"></i>
                            Browse Universities
                        </a>
                        <a href="mock-test.html" class="resource-link">
                            <i class="fas fa-brain"></i>
                            Take Mock Tests
                        </a>
                        <a href="documents.html" class="resource-link">
                            <i class="fas fa-file-alt"></i>
                            Manage Documents
                        </a>
                        <a href="resources.html" class="resource-link">
                            <i class="fas fa-book"></i>
                            Study Resources
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="script.js"></script>
    <script>
        // Chatbot functionality
        let messageCount = 0;
        const botResponses = {
            'lums': 'LUMS requires a minimum of 70% marks in FSc/A-levels, SAT score of 1200+, and passing their admission test. The application deadline is typically in February.',
            'mdcat': 'MDCAT 2024 is scheduled for August 25th. Registration usually opens in June. You need FSc Pre-Medical with at least 60% marks to be eligible.',
            'documents': 'Required documents typically include: Matric Certificate, FSc/A-level Certificate, CNIC copy, Passport photos, Character Certificate, and Migration Certificate (if applicable).',
            'engineering': 'Top engineering universities in Lahore include UET, LUMS, PU Engineering, and COMSATS. Each has different admission criteria and specializations.',
            'medical fees': 'Government medical colleges: PKR 50,000-100,000/year. Private medical colleges: PKR 800,000-2,000,000/year. Some offer scholarships based on merit.',
            'test preparation': 'For entry test preparation: 1) Practice past papers, 2) Take mock tests, 3) Focus on weak areas, 4) Join preparation courses, 5) Maintain consistent study schedule.',
            'default': 'I understand you\'re asking about university admissions. Could you please be more specific? I can help with requirements, deadlines, fees, or test preparation.'
        };

        function initializeChatbot() {
            const messageInput = document.getElementById('messageInput');
            
            // Enter key to send message
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Character counter
            messageInput.addEventListener('input', function() {
                const count = this.value.length;
                document.querySelector('.character-count').textContent = `${count}/500`;
            });

            // Auto-resize input
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            input.value = '';
            input.style.height = 'auto';
            document.querySelector('.character-count').textContent = '0/500';

            // Show typing indicator
            showTypingIndicator();

            // Simulate bot response
            setTimeout(() => {
                hideTypingIndicator();
                const response = getBotResponse(message);
                addMessage(response, 'bot');
            }, 1500 + Math.random() * 1000);
        }

        function sendQuickReply(message) {
            // Hide quick replies
            document.querySelector('.quick-replies').style.display = 'none';
            
            // Send the message
            addMessage(message, 'user');
            
            // Show typing indicator
            showTypingIndicator();

            // Simulate bot response
            setTimeout(() => {
                hideTypingIndicator();
                const response = getBotResponse(message);
                addMessage(response, 'bot');
            }, 1500 + Math.random() * 1000);
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            if (sender === 'user') {
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div class="message-bubble">
                            <p>${text}</p>
                        </div>
                        <div class="message-time">${time}</div>
                    </div>
                    <div class="message-avatar">
                        <img src="https://via.placeholder.com/32x32/2563eb/ffffff?text=U" alt="User">
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble">
                            <p>${text}</p>
                        </div>
                        <div class="message-time">${time}</div>
                    </div>
                `;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            messageCount++;
        }

        function getBotResponse(message) {
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('lums')) return botResponses.lums;
            if (lowerMessage.includes('mdcat')) return botResponses.mdcat;
            if (lowerMessage.includes('document')) return botResponses.documents;
            if (lowerMessage.includes('engineering')) return botResponses.engineering;
            if (lowerMessage.includes('medical') && lowerMessage.includes('fee')) return botResponses['medical fees'];
            if (lowerMessage.includes('test') && lowerMessage.includes('prepar')) return botResponses['test preparation'];
            
            return botResponses.default;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'flex';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function toggleChatInfo() {
            const sidebar = document.getElementById('chatInfoSidebar');
            sidebar.classList.toggle('active');
        }

        function clearChat() {
            if (confirm('Are you sure you want to clear the chat history?')) {
                const messagesContainer = document.getElementById('chatMessages');
                // Keep only the welcome message
                const welcomeMessage = messagesContainer.querySelector('.bot-message');
                messagesContainer.innerHTML = '';
                messagesContainer.appendChild(welcomeMessage);
                
                // Show quick replies again
                const quickReplies = document.createElement('div');
                quickReplies.className = 'quick-replies';
                quickReplies.innerHTML = `
                    <button class="quick-reply" onclick="sendQuickReply('What are the admission requirements for LUMS?')">
                        <i class="fas fa-graduation-cap"></i>
                        LUMS Requirements
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('When is the MDCAT exam date?')">
                        <i class="fas fa-calendar"></i>
                        MDCAT Date
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('What documents do I need for university admission?')">
                        <i class="fas fa-file-alt"></i>
                        Required Documents
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('Tell me about engineering universities in Lahore')">
                        <i class="fas fa-cogs"></i>
                        Engineering Unis
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('What are the fee structures for medical colleges?')">
                        <i class="fas fa-money-bill-wave"></i>
                        Medical Fees
                    </button>
                    <button class="quick-reply" onclick="sendQuickReply('How to prepare for entry tests?')">
                        <i class="fas fa-brain"></i>
                        Test Preparation
                    </button>
                `;
                messagesContainer.appendChild(quickReplies);
                
                showNotification('Chat cleared successfully!', 'success');
                messageCount = 0;
            }
        }

        function exportChat() {
            showNotification('Chat export feature coming soon!', 'info');
        }

        function shareChat() {
            showNotification('Chat sharing feature coming soon!', 'info');
        }

        function reportIssue() {
            showNotification('Thank you for your feedback! Our team will review it.', 'success');
        }

        // Initialize chatbot when page loads
        document.addEventListener('DOMContentLoaded', initializeChatbot);
    </script>
</body>
</html>
