<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Portal Login - UniConnect Pakistan</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1e40af, #059669);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

    <div class="auth-container">
        <div class="auth-background"></div>
        
        <div class="auth-content">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-university"></i>
                    <span>UniConnect Pakistan</span>
                </div>
                <a href="index.html" class="back-home">
                    <i class="fas fa-arrow-left"></i>
                    Back to Home
                </a>
            </div>

            <div class="auth-form-container">
                <div class="auth-welcome">
                    <h2>University Portal</h2>
                    <p>Access your university's admission management system</p>
                </div>

                <form id="universityLoginForm" class="auth-form">
                    <div class="form-group">
                        <label for="universityCode">University Code</label>
                        <div class="input-group">
                            <i class="fas fa-university"></i>
                            <input type="text" id="universityCode" name="universityCode" placeholder="Enter university code" required>
                        </div>
                        <small class="form-help">Contact admin for your university code</small>
                    </div>

                    <div class="form-group">
                        <label for="adminEmail">Admin Email</label>
                        <div class="input-group">
                            <i class="fas fa-user-tie"></i>
                            <input type="email" id="adminEmail" name="adminEmail" placeholder="Enter admin email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="Enter password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember">
                            <span class="checkmark"></span>
                            Keep me signed in
                        </label>
                        <a href="#" class="forgot-password">Forgot Password?</a>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full" id="universityLoginBtn" onclick="handleUniversityLogin(); return false;">
                        <i class="fas fa-sign-in-alt"></i>
                        Access University Portal
                    </button>

                    <div class="auth-divider">
                        <span>or</span>
                    </div>

                    <button type="button" class="btn btn-outline btn-full" onclick="requestAccess()">
                        <i class="fas fa-plus"></i>
                        Request University Access
                    </button>
                </form>

                <div class="demo-credentials">
                    <div class="demo-header">
                        <i class="fas fa-info-circle"></i>
                        <strong>Demo Credentials</strong>
                    </div>
                    <div class="demo-details">
                        <p><strong>University Code:</strong> PU2024</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> university123</p>
                        <button onclick="fillDemoCredentials()" class="btn btn-outline btn-sm" style="margin-top: 10px;">
                            <i class="fas fa-magic"></i>
                            Auto-fill Demo Credentials
                        </button>
                    </div>
                </div>

                <div class="university-features">
                    <h4>University Portal Features</h4>
                    <div class="features-grid">
                        <div class="feature-item">
                            <i class="fas fa-file-alt"></i>
                            <span>Application Management</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Program Management</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-check-alt"></i>
                            <span>Challan Generation</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Analytics & Reports</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Student Management</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-bell"></i>
                            <span>Notifications</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple working university login
        function handleUniversityLogin() {
            console.log('Login function called!');

            const universityCode = document.getElementById('universityCode').value;
            const adminEmail = document.getElementById('adminEmail').value;
            const password = document.getElementById('password').value;

            console.log('Values:', universityCode, adminEmail, password);

            if (!universityCode || !adminEmail || !password) {
                alert('Please fill in all fields!');
                return false;
            }

            // Show loading
            const btn = document.getElementById('universityLoginBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
            btn.disabled = true;

            // Check credentials
            setTimeout(() => {
                if (universityCode === 'PU2024' && adminEmail === '<EMAIL>' && password === 'university123') {
                    alert('Login successful! Redirecting to dashboard...');
                    window.location.href = 'university-dashboard.html';
                } else {
                    alert('Invalid credentials! Use: PU2024 / <EMAIL> / university123');
                    btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Access University Portal';
                    btn.disabled = false;
                }
            }, 1000);

            return false;
        }

        // Attach to form
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            const form = document.getElementById('universityLoginForm');
            if (form) {
                console.log('Form found, attaching event');
                form.onsubmit = function(e) {
                    e.preventDefault();
                    handleUniversityLogin();
                    return false;
                };
            } else {
                console.log('Form not found!');
            }
        });

        // Auto-fill demo credentials
        function fillDemoCredentials() {
            document.getElementById('universityCode').value = 'PU2024';
            document.getElementById('adminEmail').value = '<EMAIL>';
            document.getElementById('password').value = 'university123';
            alert('Demo credentials filled! Now click "Access University Portal" button.');
        }

        // Theme toggle function
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            document.querySelectorAll('.theme-option').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.theme-option.${theme}`).classList.add('active');
        }
    </script>

    <style>
        /* University Login Specific Styles */
        .auth-welcome h2 {
            color: var(--secondary-color);
        }

        .form-help {
            font-size: 12px;
            color: var(--text-muted);
            margin-top: 5px;
            display: block;
        }

        .university-features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid var(--border-color);
        }

        .university-features h4 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: var(--text-secondary);
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
        }

        .feature-item i {
            color: var(--secondary-color);
            width: 16px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
