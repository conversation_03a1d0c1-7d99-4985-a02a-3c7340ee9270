// Global variables
let currentSlide = 1;
const totalSlides = 4;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    initializeOnboarding();
    initializeNavigation();
    initializeSmoothScrolling();
});

// Theme Management
function initializeTheme() {
    // Check for saved theme preference or default to light mode
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
}

function setTheme(theme) {
    // Update document attribute
    document.documentElement.setAttribute('data-theme', theme);

    // Save preference
    localStorage.setItem('theme', theme);

    // Update toggle buttons
    const lightBtn = document.querySelector('.theme-option.light');
    const darkBtn = document.querySelector('.theme-option.dark');

    if (lightBtn && darkBtn) {
        lightBtn.classList.toggle('active', theme === 'light');
        darkBtn.classList.toggle('active', theme === 'dark');
    }

    // Smooth transition for theme change
    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    setTimeout(() => {
        document.body.style.transition = '';
    }, 300);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

// Onboarding functionality
function initializeOnboarding() {
    // Auto-advance slides every 5 seconds if user doesn't interact
    const autoAdvanceTimer = setInterval(() => {
        if (currentSlide < totalSlides) {
            nextSlide();
        } else {
            clearInterval(autoAdvanceTimer);
        }
    }, 5000);

    // Clear timer if user interacts
    document.addEventListener('click', () => {
        clearInterval(autoAdvanceTimer);
    });
}

function nextSlide() {
    if (currentSlide < totalSlides) {
        // Hide current slide
        document.querySelector(`.onboarding-slide[data-slide="${currentSlide}"]`).classList.remove('active');
        document.querySelector(`.dot[data-dot="${currentSlide}"]`).classList.remove('active');
        
        // Show next slide
        currentSlide++;
        document.querySelector(`.onboarding-slide[data-slide="${currentSlide}"]`).classList.add('active');
        document.querySelector(`.dot[data-dot="${currentSlide}"]`).classList.add('active');
        
        // Update button text on last slide
        if (currentSlide === totalSlides) {
            document.querySelector('.next-btn').textContent = 'Get Started';
            document.querySelector('.next-btn').onclick = skipOnboarding;
        }
    } else {
        skipOnboarding();
    }
}

function skipOnboarding() {
    const onboardingContainer = document.getElementById('onboardingContainer');
    const mainContent = document.getElementById('mainContent');
    
    // Fade out onboarding
    onboardingContainer.style.opacity = '0';
    onboardingContainer.style.transform = 'scale(0.9)';
    
    setTimeout(() => {
        onboardingContainer.style.display = 'none';
        const userTypeSelection = document.getElementById('userTypeSelection');
        if (userTypeSelection) {
            userTypeSelection.style.display = 'flex';
            userTypeSelection.style.opacity = '0';

            // Fade in user type selection
            setTimeout(() => {
                userTypeSelection.style.opacity = '1';
            }, 100);
        } else {
            mainContent.style.display = 'block';
            mainContent.style.opacity = '0';

            // Fade in main content
            setTimeout(() => {
                mainContent.style.opacity = '1';
            }, 100);
        }

        // Save onboarding completion
        localStorage.setItem('onboardingCompleted', 'true');
    }, 500);
}

// Navigation functionality
function initializeNavigation() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });
    }

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    });
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Utility functions for other pages
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

// Loading state management
function showLoading(element) {
    const originalText = element.textContent;
    element.textContent = 'Loading...';
    element.disabled = true;
    element.classList.add('loading');
    
    return () => {
        element.textContent = originalText;
        element.disabled = false;
        element.classList.remove('loading');
    };
}

// Local storage helpers
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('Error saving to storage:', error);
        return false;
    }
}

function getFromStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error reading from storage:', error);
        return null;
    }
}

// User Type Selection Functions
function completeOnboarding() {
    const onboardingContainer = document.getElementById('onboardingContainer');
    const userTypeSelection = document.getElementById('userTypeSelection');

    if (onboardingContainer && userTypeSelection) {
        onboardingContainer.style.display = 'none';
        userTypeSelection.style.display = 'flex';

        // Save onboarding completion
        localStorage.setItem('onboardingCompleted', 'true');
    }
}

function selectUserType(userType) {
    // Save user type selection
    localStorage.setItem('selectedUserType', userType);

    // Show loading message
    showNotification(`Redirecting to ${userType} portal...`, 'info');

    // Redirect based on user type
    setTimeout(() => {
        switch(userType) {
            case 'student':
                window.location.href = 'login.html?type=student';
                break;
            case 'university':
                window.location.href = 'university-login.html';
                break;
            case 'admin':
                window.location.href = 'admin-login.html';
                break;
            default:
                window.location.href = 'login.html';
        }
    }, 1500);
}

function showMainContent() {
    const userTypeSelection = document.getElementById('userTypeSelection');
    const mainContent = document.getElementById('mainContent');

    if (userTypeSelection && mainContent) {
        userTypeSelection.style.display = 'none';
        mainContent.style.display = 'block';
    }
}

// Enhanced Notification System
function showNotification(message, type = 'info', duration = 5000) {
    // Remove existing notifications of the same type
    const existingNotifications = document.querySelectorAll(`.notification.${type}`);
    existingNotifications.forEach(notif => notif.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="closeNotification(this)">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Trigger animation
    setTimeout(() => notification.classList.add('show'), 10);

    // Auto remove after specified duration
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                closeNotification(notification.querySelector('.notification-close'));
            }
        }, duration);
    }

    return notification;
}

function getNotificationIcon(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

function closeNotification(button) {
    const notification = button.closest('.notification');
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 300);
}

// Loading state management for buttons
function showLoading(button, text = null) {
    const originalText = button.innerHTML;
    const originalDisabled = button.disabled;

    button.classList.add('loading');
    button.disabled = true;

    if (text) {
        button.innerHTML = text;
    }

    // Return function to hide loading
    return function hideLoading() {
        button.classList.remove('loading');
        button.disabled = originalDisabled;
        button.innerHTML = originalText;
    };
}

// Unified form validation
function validateForm(formElement) {
    const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });

    return isValid;
}

// Unified modal system
function showModal(content, options = {}) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${options.title || 'Confirmation'}</h3>
                <button class="modal-close" onclick="closeModal(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal(this)">Cancel</button>
                <button class="btn btn-primary" onclick="${options.onConfirm || 'closeModal(this)'}">
                    ${options.confirmText || 'Confirm'}
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('active'), 10);
}

function closeModal(element) {
    const modal = element.closest('.modal-overlay');
    modal.classList.remove('active');
    setTimeout(() => modal.remove(), 300);
}

// Mock data for demo purposes
const mockUniversities = [
    {
        id: 1,
        name: "University of Punjab",
        city: "Lahore",
        logo: "fas fa-university",
        programs: ["Computer Science", "Business Administration", "Engineering"],
        deadline: "2024-09-15",
        requirements: ["Matric Certificate", "FSc Certificate", "CNIC Copy"],
        entryTest: true
    },
    {
        id: 2,
        name: "Karachi University",
        city: "Karachi",
        logo: "fas fa-graduation-cap",
        programs: ["Medicine", "Law", "Arts"],
        deadline: "2024-08-30",
        requirements: ["Matric Certificate", "FSc Certificate", "CNIC Copy"],
        entryTest: true
    },
    {
        id: 3,
        name: "LUMS",
        city: "Lahore",
        logo: "fas fa-university",
        programs: ["MBA", "Computer Science", "Economics"],
        deadline: "2024-10-01",
        requirements: ["Bachelor's Degree", "Transcript", "CNIC Copy"],
        entryTest: true
    }
];

const mockNotifications = [
    {
        id: 1,
        title: "Admission Deadline Approaching",
        message: "University of Punjab deadline is in 3 days",
        type: "warning",
        date: "2024-08-07",
        read: false
    },
    {
        id: 2,
        title: "Mock Test Available",
        message: "New mock test for MDCAT is now available",
        type: "info",
        date: "2024-08-06",
        read: false
    },
    {
        id: 3,
        title: "Application Submitted",
        message: "Your application to LUMS has been submitted successfully",
        type: "success",
        date: "2024-08-05",
        read: true
    }
];

// Date formatting utility
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Calculate days until deadline
function getDaysUntilDeadline(deadline) {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

// Get deadline status
function getDeadlineStatus(deadline) {
    const days = getDaysUntilDeadline(deadline);
    if (days < 0) return 'expired';
    if (days <= 3) return 'urgent';
    if (days <= 7) return 'warning';
    return 'normal';
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = event.target.getAttribute('data-tooltip');
    document.body.appendChild(tooltip);
    
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
}

function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    const searchResults = mockUniversities.filter(uni => 
        uni.name.toLowerCase().includes(query) ||
        uni.city.toLowerCase().includes(query) ||
        uni.programs.some(program => program.toLowerCase().includes(query))
    );
    
    displaySearchResults(searchResults);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize page-specific functionality
function initializePage() {
    const page = document.body.getAttribute('data-page');
    
    switch (page) {
        case 'dashboard':
            initializeDashboard();
            break;
        case 'universities':
            initializeUniversitiesList();
            break;
        case 'notifications':
            initializeNotifications();
            break;
        case 'mock-test':
            initializeMockTest();
            break;
        default:
            break;
    }
    
    initializeTooltips();
    initializeSearch();
}

// Call initialization when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
} else {
    initializePage();
}
