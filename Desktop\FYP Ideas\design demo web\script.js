// Global variables
let currentSlide = 1;
const totalSlides = 4;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeOnboarding();
    initializeNavigation();
    initializeSmoothScrolling();
});

// Onboarding functionality
function initializeOnboarding() {
    // Auto-advance slides every 5 seconds if user doesn't interact
    const autoAdvanceTimer = setInterval(() => {
        if (currentSlide < totalSlides) {
            nextSlide();
        } else {
            clearInterval(autoAdvanceTimer);
        }
    }, 5000);

    // Clear timer if user interacts
    document.addEventListener('click', () => {
        clearInterval(autoAdvanceTimer);
    });
}

function nextSlide() {
    if (currentSlide < totalSlides) {
        // Hide current slide
        document.querySelector(`.onboarding-slide[data-slide="${currentSlide}"]`).classList.remove('active');
        document.querySelector(`.dot[data-dot="${currentSlide}"]`).classList.remove('active');
        
        // Show next slide
        currentSlide++;
        document.querySelector(`.onboarding-slide[data-slide="${currentSlide}"]`).classList.add('active');
        document.querySelector(`.dot[data-dot="${currentSlide}"]`).classList.add('active');
        
        // Update button text on last slide
        if (currentSlide === totalSlides) {
            document.querySelector('.next-btn').textContent = 'Get Started';
            document.querySelector('.next-btn').onclick = skipOnboarding;
        }
    } else {
        skipOnboarding();
    }
}

function skipOnboarding() {
    const onboardingContainer = document.getElementById('onboardingContainer');
    const mainContent = document.getElementById('mainContent');
    
    // Fade out onboarding
    onboardingContainer.style.opacity = '0';
    onboardingContainer.style.transform = 'scale(0.9)';
    
    setTimeout(() => {
        onboardingContainer.style.display = 'none';
        mainContent.style.display = 'block';
        mainContent.style.opacity = '0';
        
        // Fade in main content
        setTimeout(() => {
            mainContent.style.opacity = '1';
        }, 100);
    }, 500);
}

// Navigation functionality
function initializeNavigation() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });
    }

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    });
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Utility functions for other pages
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

// Loading state management
function showLoading(element) {
    const originalText = element.textContent;
    element.textContent = 'Loading...';
    element.disabled = true;
    element.classList.add('loading');
    
    return () => {
        element.textContent = originalText;
        element.disabled = false;
        element.classList.remove('loading');
    };
}

// Local storage helpers
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('Error saving to storage:', error);
        return false;
    }
}

function getFromStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error reading from storage:', error);
        return null;
    }
}

// Mock data for demo purposes
const mockUniversities = [
    {
        id: 1,
        name: "University of Punjab",
        city: "Lahore",
        logo: "fas fa-university",
        programs: ["Computer Science", "Business Administration", "Engineering"],
        deadline: "2024-09-15",
        requirements: ["Matric Certificate", "FSc Certificate", "CNIC Copy"],
        entryTest: true
    },
    {
        id: 2,
        name: "Karachi University",
        city: "Karachi",
        logo: "fas fa-graduation-cap",
        programs: ["Medicine", "Law", "Arts"],
        deadline: "2024-08-30",
        requirements: ["Matric Certificate", "FSc Certificate", "CNIC Copy"],
        entryTest: true
    },
    {
        id: 3,
        name: "LUMS",
        city: "Lahore",
        logo: "fas fa-university",
        programs: ["MBA", "Computer Science", "Economics"],
        deadline: "2024-10-01",
        requirements: ["Bachelor's Degree", "Transcript", "CNIC Copy"],
        entryTest: true
    }
];

const mockNotifications = [
    {
        id: 1,
        title: "Admission Deadline Approaching",
        message: "University of Punjab deadline is in 3 days",
        type: "warning",
        date: "2024-08-07",
        read: false
    },
    {
        id: 2,
        title: "Mock Test Available",
        message: "New mock test for MDCAT is now available",
        type: "info",
        date: "2024-08-06",
        read: false
    },
    {
        id: 3,
        title: "Application Submitted",
        message: "Your application to LUMS has been submitted successfully",
        type: "success",
        date: "2024-08-05",
        read: true
    }
];

// Date formatting utility
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Calculate days until deadline
function getDaysUntilDeadline(deadline) {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

// Get deadline status
function getDeadlineStatus(deadline) {
    const days = getDaysUntilDeadline(deadline);
    if (days < 0) return 'expired';
    if (days <= 3) return 'urgent';
    if (days <= 7) return 'warning';
    return 'normal';
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = event.target.getAttribute('data-tooltip');
    document.body.appendChild(tooltip);
    
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }
}

function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    const searchResults = mockUniversities.filter(uni => 
        uni.name.toLowerCase().includes(query) ||
        uni.city.toLowerCase().includes(query) ||
        uni.programs.some(program => program.toLowerCase().includes(query))
    );
    
    displaySearchResults(searchResults);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize page-specific functionality
function initializePage() {
    const page = document.body.getAttribute('data-page');
    
    switch (page) {
        case 'dashboard':
            initializeDashboard();
            break;
        case 'universities':
            initializeUniversitiesList();
            break;
        case 'notifications':
            initializeNotifications();
            break;
        case 'mock-test':
            initializeMockTest();
            break;
        default:
            break;
    }
    
    initializeTooltips();
    initializeSearch();
}

// Call initialization when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePage);
} else {
    initializePage();
}
